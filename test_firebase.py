#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار اتصال Firebase Storage
"""

import os
import sys

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    print("🔄 اختبار اتصال Firebase Storage...")
    
    try:
        # استيراد Firebase
        from firebase_config import firebase_manager
        
        # التحقق من وجود ملف service account
        service_account_path = "firebase-service-account.json"
        if not os.path.exists(service_account_path):
            print("❌ ملف service account غير موجود!")
            print(f"   الرجاء إنشاء الملف: {service_account_path}")
            return False
        
        # محاولة تهيئة Firebase
        print("🔄 تهيئة Firebase...")
        success = firebase_manager.initialize_with_default_config(service_account_path)
        
        if success:
            print("✅ تم تهيئة Firebase بنجاح!")
            
            # اختبار الاتصال
            print("🔄 اختبار الاتصال...")
            if firebase_manager.test_connection():
                print("✅ اختبار الاتصال نجح!")
                
                # اختبار رفع ملف تجريبي
                print("🔄 اختبار رفع ملف تجريبي...")
                test_content = b"Hello Firebase Storage!"
                test_filename = "test_file.txt"
                
                url = firebase_manager.upload_file_to_storage(
                    test_content, 
                    test_filename, 
                    "text/plain", 
                    "test"
                )
                
                if url:
                    print(f"✅ تم رفع الملف التجريبي بنجاح!")
                    print(f"🔗 الرابط: {url}")
                    
                    # حذف الملف التجريبي
                    print("🔄 حذف الملف التجريبي...")
                    if firebase_manager.delete_file(f"test/{test_filename}"):
                        print("✅ تم حذف الملف التجريبي بنجاح!")
                    
                    return True
                else:
                    print("❌ فشل رفع الملف التجريبي!")
                    return False
            else:
                print("❌ فشل اختبار الاتصال!")
                return False
        else:
            print("❌ فشل تهيئة Firebase!")
            return False
            
    except ImportError as e:
        print("❌ خطأ في استيراد Firebase!")
        print(f"   {e}")
        print("   الرجاء تثبيت Firebase Admin SDK:")
        print("   pip install firebase-admin")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("    اختبار Firebase Storage")
    print("=" * 50)
    print()
    
    success = test_firebase_connection()
    
    print()
    print("=" * 50)
    if success:
        print("✅ جميع الاختبارات نجحت!")
        print("🎉 Firebase Storage جاهز للاستخدام!")
    else:
        print("❌ فشلت بعض الاختبارات!")
        print("📖 راجع دليل الإعداد: FIREBASE_SETUP_GUIDE.md")
    print("=" * 50)

if __name__ == "__main__":
    main()
