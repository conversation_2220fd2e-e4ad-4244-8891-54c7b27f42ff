# -*- coding: utf-8 -*-
"""
Enhanced Integration Module
This module provides enhanced integration between different components of the mod tools.
"""

import os
import json
import time
from typing import Dict, List, Optional, Any, Tuple

class EnhancedIntegration:
    """
    A class that provides enhanced integration between different components.
    """
    
    def __init__(self):
        """Initialize the EnhancedIntegration."""
        self.components = {}
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from the config file.
        
        Returns:
            A dictionary containing configuration
        """
        config_file = "enhanced_integration_config.json"
        default_config = {
            "enabled": True,
            "components": {},
            "settings": {
                "auto_enhance_images": True,
                "auto_extract_social": True,
                "auto_backup": True
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with default config
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                print(f"⚠️ Error loading enhanced integration config: {e}")
                return default_config
        else:
            # Create default config file
            try:
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
            except Exception as e:
                print(f"⚠️ Error creating enhanced integration config: {e}")
            
            return default_config
    
    def _save_config(self):
        """Save configuration to the config file."""
        config_file = "enhanced_integration_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Error saving enhanced integration config: {e}")
    
    def register_component(self, name: str, component: Any) -> bool:
        """
        Register a component with the integration.
        
        Args:
            name: The name of the component
            component: The component object
            
        Returns:
            True if registration was successful, False otherwise
        """
        if name in self.components:
            print(f"⚠️ Component '{name}' already registered")
            return False
        
        self.components[name] = component
        
        # Update config
        if "components" not in self.config:
            self.config["components"] = {}
        self.config["components"][name] = {"registered": True, "registered_at": time.time()}
        self._save_config()
        
        print(f"✅ Component '{name}' registered successfully")
        return True
    
    def unregister_component(self, name: str) -> bool:
        """
        Unregister a component from the integration.
        
        Args:
            name: The name of the component
            
        Returns:
            True if unregistration was successful, False otherwise
        """
        if name not in self.components:
            print(f"⚠️ Component '{name}' not registered")
            return False
        
        del self.components[name]
        
        # Update config
        if "components" in self.config and name in self.config["components"]:
            self.config["components"][name]["registered"] = False
            self.config["components"][name]["unregistered_at"] = time.time()
        self._save_config()
        
        print(f"✅ Component '{name}' unregistered successfully")
        return True
    
    def get_component(self, name: str) -> Optional[Any]:
        """
        Get a registered component.
        
        Args:
            name: The name of the component
            
        Returns:
            The component object, or None if not found
        """
        return self.components.get(name)
    
    def list_components(self) -> List[str]:
        """
        List all registered components.
        
        Returns:
            A list of component names
        """
        return list(self.components.keys())
    
    def is_component_registered(self, name: str) -> bool:
        """
        Check if a component is registered.
        
        Args:
            name: The name of the component
            
        Returns:
            True if the component is registered, False otherwise
        """
        return name in self.components
    
    def get_setting(self, name: str, default: Any = None) -> Any:
        """
        Get a setting value.
        
        Args:
            name: The name of the setting
            default: The default value to return if the setting is not found
            
        Returns:
            The setting value, or the default value if not found
        """
        if "settings" not in self.config:
            return default
        
        return self.config["settings"].get(name, default)
    
    def set_setting(self, name: str, value: Any) -> bool:
        """
        Set a setting value.
        
        Args:
            name: The name of the setting
            value: The value to set
            
        Returns:
            True if the setting was set successfully, False otherwise
        """
        if "settings" not in self.config:
            self.config["settings"] = {}
        
        self.config["settings"][name] = value
        self._save_config()
        
        return True
    
    def is_enabled(self) -> bool:
        """
        Check if enhanced integration is enabled.
        
        Returns:
            True if enabled, False otherwise
        """
        return self.config.get("enabled", True)
    
    def enable(self) -> bool:
        """
        Enable enhanced integration.
        
        Returns:
            True if successful, False otherwise
        """
        self.config["enabled"] = True
        self._save_config()
        
        return True
    
    def disable(self) -> bool:
        """
        Disable enhanced integration.
        
        Returns:
            True if successful, False otherwise
        """
        self.config["enabled"] = False
        self._save_config()
        
        return True

# Create a singleton instance
_instance = None

def get_enhanced_integration() -> EnhancedIntegration:
    """
    Get the EnhancedIntegration singleton instance.
    
    Returns:
        The EnhancedIntegration instance
    """
    global _instance
    if _instance is None:
        _instance = EnhancedIntegration()
    return _instance