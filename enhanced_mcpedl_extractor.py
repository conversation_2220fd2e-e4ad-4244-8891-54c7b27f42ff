# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL محسن مع فلترة ذكية للصور ومواقع التواصل الاجتماعي
Enhanced MCPEDL Extractor with Smart Image Filtering and Social Media Detection
"""

import os
import re
import json
import time
import base64
import requests
from typing import List, Dict, Optional, Any, Tuple
from urllib.parse import urljoin, urlparse
import html
from bs4 import BeautifulSoup

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    print("⚠️ google-generativeai غير متوفر. سيتم تعطيل الفلترة الذكية.")

try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("⚠️ cloudscraper غير متوفر. سيتم استخدام requests العادي.")

try:
    from mcpedl_extractor_enhancements import MCPEDLExtractorEnhancements
    ENHANCEMENTS_AVAILABLE = True
except ImportError:
    ENHANCEMENTS_AVAILABLE = False
    print("⚠️ تحسينات المستخرج غير متوفرة.")


class EnhancedMCPEDLExtractor:
    """مستخرج MCPEDL محسن مع ذكاء اصطناعي"""
    
    def __init__(self, gemini_api_key: str = None):
        """تهيئة المستخرج المحسن"""
        self.gemini_api_key = gemini_api_key or os.environ.get("GEMINI_API_KEY")
        self.gemini_model = None

        # تهيئة التحسينات الإضافية
        if ENHANCEMENTS_AVAILABLE:
            self.enhancements = MCPEDLExtractorEnhancements()
            print("✅ تم تحميل التحسينات الإضافية")
        else:
            self.enhancements = None

        # تهيئة Gemini إذا كان متوفراً
        if GEMINI_AVAILABLE and self.gemini_api_key:
            try:
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                print("✅ تم تهيئة Gemini AI للفلترة الذكية")
            except Exception as e:
                print(f"⚠️ خطأ في تهيئة Gemini: {e}")
                self.gemini_model = None
        
        # محددات CSS محسنة لـ MCPEDL
        self.selectors = {
            'title': 'h1.entry-title, .post-title h1, .mod-title, .addon-title, h1, .title, .post-title',
            'description': '.entry-content, .post-content, .mod-description, .addon-description, .content, .description, .post-body',
            'category': '.post-categories a, .category-link, .mod-category, .category, .post-category',
            'main_image': '.featured-image img, .post-thumbnail img, .mod-main-image img, .wp-post-image',
            'gallery_images': '.gallery img, .mod-gallery img, .screenshots img, .wp-block-gallery img, .entry-content img, .content img, .post-content img',
            'download_links': '.download-link, .btn-download, a[href*="download"], .download-button, .download',
            'creator_info': '.author-info, .post-author, .creator-name, .by-author, .author, .post-meta .author',
            'social_links': '.social-links a, .author-social a, .contact-links a, .social a',
            'version_info': '.version-info, .mod-version, .addon-version, .version',
            'file_size': '.file-size, .download-size, .size-info, .size'
        }
        
        # أنماط لتجنب صور المودات المقترحة
        self.avoid_patterns = [
            'related-posts',
            'suggested-mods', 
            'other-mods',
            'recommended',
            'similar-addons',
            'you-might-like',
            'more-from-author',
            'gravatar',
            'avatar',
            'profile',
            'comment',
            'user-image',
            'author-photo'
        ]
        
        # أنماط مواقع التواصل الاجتماعي لتجنبها (خاصة بـ MCPEDL)
        self.mcpedl_share_patterns = [
            'mcpedl.com/share',
            'facebook.com/sharer',
            'twitter.com/intent/tweet',
            'pinterest.com/pin/create',
            'reddit.com/submit',
            'tumblr.com/share',
            'vk.com/share',
            'telegram.me/share'
        ]

    def extract_mod_data(self, url: str) -> Optional[Dict[str, Any]]:
        """استخراج بيانات المود بشكل محسن"""
        print(f"🔍 بدء استخراج البيانات من: {url}")
        
        # جلب الصفحة
        html_content = self._fetch_page_content(url)
        if not html_content:
            return None
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # استخراج البيانات الأساسية
        mod_data = {
            'name': self._extract_title(soup),
            'description': self._extract_description(soup),
            'category': self._extract_category(soup),
            'version': self._extract_version(soup),
            'creator_name': self._extract_creator_name(soup),
            'source_url': url
        }
        
        # استخراج الصور بفلترة ذكية
        print("🖼️ بدء استخراج الصور...")
        mod_data['image_urls'] = self._extract_images_smart(soup, url, mod_data['name'], html_content)
        
        # استخراج مواقع التواصل الاجتماعي بذكاء
        print("🌐 بدء استخراج مواقع التواصل الاجتماعي...")
        social_data = self._extract_social_media_smart(html_content, url, mod_data['name'])
        mod_data.update(social_data)
        
        # استخراج معلومات التحميل
        download_info = self._extract_download_info(soup, url)
        mod_data.update(download_info)

        # توليد الأوصاف الإضافية باستخدام Gemini
        if self.gemini_model and mod_data.get('name') and mod_data.get('description'):
            print("🤖 توليد الأوصاف الإضافية باستخدام Gemini...")
            additional_descriptions = self._generate_additional_descriptions(mod_data)
            mod_data.update(additional_descriptions)

        # تقييم جودة الاستخراج
        if self.enhancements:
            quality_report = self.enhancements.validate_extraction_quality(mod_data)
            mod_data['quality_report'] = quality_report

            print(f"📊 تقييم الجودة: {quality_report['overall_score']:.1f}/100")
            if quality_report['issues']:
                for issue in quality_report['issues']:
                    print(f"⚠️ {issue}")
            if quality_report['recommendations']:
                for rec in quality_report['recommendations']:
                    print(f"💡 {rec}")

        print(f"✅ تم استخراج البيانات بنجاح للمود: {mod_data['name']}")
        return mod_data

    def _fetch_page_content(self, url: str) -> Optional[str]:
        """جلب محتوى الصفحة"""
        try:
            if CLOUDSCRAPER_AVAILABLE:
                scraper = cloudscraper.create_scraper(
                    browser={
                        'browser': 'chrome',
                        'platform': 'windows',
                        'mobile': False
                    }
                )
            else:
                scraper = requests.Session()
                scraper.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
            
            response = scraper.get(url, timeout=30)
            response.raise_for_status()
            
            if len(response.text) < 5000:
                print("⚠️ محتوى الصفحة قصير جداً")
                return None
                
            return response.text
            
        except Exception as e:
            print(f"❌ خطأ في جلب الصفحة: {e}")
            return None

    def _extract_images_smart(self, soup: BeautifulSoup, base_url: str, mod_name: str, html_content: str) -> List[str]:
        """استخراج الصور مع فلترة ذكية محسنة"""
        print("🖼️ بدء استخراج الصور بفلترة ذكية...")
        all_images = []

        # 1. استخراج جميع الصور المحتملة بأولوية
        image_selectors_priority = [
            # أولوية عالية - صور رئيسية
            ('.featured-image img', 'featured'),
            ('.post-thumbnail img', 'thumbnail'),
            ('.mod-main-image img', 'main'),
            ('img[src*="media.forgecdn.net/attachments/description/"]', 'forgecdn_description'), # صور المود الأصلية
            ('img[src*="forgecdn"]', 'forgecdn'),  # صور ForgeCD (موثوقة)
            ('img[src*="media.forgecdn.net"]', 'forgecdn'),  # صور ForgeCD

            # أولوية متوسطة - معارض الصور
            ('.gallery img', 'gallery'),
            ('.mod-gallery img', 'mod_gallery'),
            ('.screenshots img', 'screenshots'),
            ('.wp-block-gallery img', 'wp_gallery'),
            ('img[src*="r2.mcpedl.com"]', 'mcpedl'),  # صور MCPEDL

            # أولوية منخفضة - صور المحتوى
            ('.entry-content img', 'content'),
            ('.content img', 'content'),
            ('.post-content img', 'post_content'),
            
            # MCPEDL specific selectors
            ('.post-body img', 'mcpedl_body'),
            ('.addon-screenshots img', 'mcpedl_screenshots'),
            ('.addon-gallery img', 'mcpedl_gallery'),
            ('.wp-block-image img', 'mcpedl_block_image'),
            ('.wp-block-image figure img', 'mcpedl_figure'),
            ('.entry img', 'mcpedl_entry'),

            # أولوية منخفضة جداً - جميع الصور
            ('img', 'general')
        ]

        image_sources = {}  # تتبع مصدر كل صورة

        # إضافة صور ForgeCDN Description مباشرة باستخدام regex
        forgecdn_desc_pattern = r'https://media\.forgecdn\.net/attachments/description/\d+/description_[a-f0-9-]+\.png'
        found_forgecdn_desc_images = re.findall(forgecdn_desc_pattern, html_content)
        for url in found_forgecdn_desc_images:
            if self._is_valid_image_url(url) and url not in all_images:
                all_images.append(url)
                image_sources[url] = 'forgecdn_description'
                print(f"   ✅ صورة من forgecdn_description (regex): {url[:60]}...")

        # استخراج الصور من الـ style background-image
        background_images = re.findall(r'background-image:\s*url\([\'"]?(.*?)[\'"]?\)', html_content)
        for bg_url in background_images:
            full_url = urljoin(base_url, bg_url)
            if self._is_valid_image_url(full_url) and full_url not in all_images:
                all_images.append(full_url)
                image_sources[full_url] = 'background_image'
                print(f"   ✅ صورة من background-image: {full_url[:60]}...")

        # استخراج الصور من الـ data-src و data-original
        data_src_pattern = r'data-(?:src|original|lazy-src|srcset)=[\'"]([^\'"]+)[\'"]'
        data_src_images = re.findall(data_src_pattern, html_content)
        for data_src in data_src_images:
            full_url = urljoin(base_url, data_src.split(' ')[0])  # في حالة srcset نأخذ أول URL
            if self._is_valid_image_url(full_url) and full_url not in all_images:
                all_images.append(full_url)
                image_sources[full_url] = 'data_src'
                print(f"   ✅ صورة من data-src: {full_url[:60]}...")
                
        # استخراج الصور من الـ srcset
        srcset_pattern = r'srcset=[\'"]([^\'"]+)[\'"]'
        srcset_images = re.findall(srcset_pattern, html_content)
        for srcset in srcset_images:
            # استخراج أول URL من srcset (عادة الأكبر حجماً)
            first_url = srcset.split(',')[0].strip().split(' ')[0]
            full_url = urljoin(base_url, first_url)
            if self._is_valid_image_url(full_url) and full_url not in all_images:
                all_images.append(full_url)
                image_sources[full_url] = 'srcset'
                print(f"   ✅ صورة من srcset: {full_url[:60]}...")

        for selector, source_type in image_selectors_priority:
            images = soup.select(selector)
            for img in images:
                # البحث عن مصدر الصورة بطرق متعددة
                src = (img.get('src') or
                      img.get('data-src') or
                      img.get('data-lazy-src') or
                      img.get('data-original') or
                      img.get('data-srcset', '').split(',')[0].strip().split(' ')[0])

                if src:
                    full_url = urljoin(base_url, src)
                    if self._is_valid_image_url(full_url) and full_url not in all_images:
                        all_images.append(full_url)
                        image_sources[full_url] = source_type
                        print(f"   ✅ صورة من {source_type}: {full_url[:60]}...")

        print(f"📋 تم العثور على {len(all_images)} صورة محتملة")

        # 2. فلترة أولية محسنة
        filtered_images = self._enhanced_pre_filter_images(all_images, image_sources)
        print(f"🔍 بعد الفلترة الأولية المحسنة: {len(filtered_images)} صورة")

        # 3. فلترة متقدمة إذا كانت متوفرة
        if self.enhancements and len(filtered_images) > 5:
            try:
                advanced_filtered = self.enhancements.advanced_image_filter(filtered_images, mod_name)
                if advanced_filtered:
                    filtered_images = advanced_filtered
                    print(f"🔍 بعد الفلترة المتقدمة: {len(filtered_images)} صورة")
            except Exception as e:
                print(f"⚠️ خطأ في الفلترة المتقدمة: {e}")

        # 4. فلترة ذكية باستخدام Gemini إذا كان متوفراً
        if self.gemini_model and len(filtered_images) > 3:
            smart_filtered = self._gemini_filter_images(filtered_images, mod_name)
            if smart_filtered:
                print(f"🤖 Gemini اختار {len(smart_filtered)} صور نهائية")
                return smart_filtered

        # 5. إرجاع أفضل الصور مع ترتيب حسب الأولوية
        final_images = self._prioritize_images(filtered_images, image_sources)
        return final_images[:8]  # حد أقصى 8 صور

    def _enhanced_pre_filter_images(self, image_urls: List[str], image_sources: Dict[str, str]) -> List[str]:
        """فلترة أولية محسنة للصور"""
        filtered = []

        # أنماط محسنة للصور غير المرغوبة (مع تقليل الصرامة)
        avoid_patterns = [
            # أيقونات وشعارات واضحة
            'empty.png', 'arrow.svg', 'icon.png', 'logo.png', 'favicon',
            'sprite.png', 'button.png', 'bg.jpg', 'background.jpg',

            # صور المستخدمين
            'gravatar', 'avatar', 'profile', 'comment', 'user-image', 'author-photo', 'users/', # إضافة 'users/' لتجنب صور المستخدمين من mcpedl

            # إعلانات ومحتوى ترويجي
            'advertisement', 'ad-', '/ads/', 'sponsor', 'promo', 'banner',

            # صور صغيرة أو غير مفيدة
            'thumb_', 'thumbnail_', '_thumb', '_small', '_mini',
            '16x16', '32x32', '48x48', '64x64'
        ]

        # أنماط للصور المقترحة (فلترة أقل صرامة)
        suggested_patterns = [
            'related-posts', 'suggested-mods', 'other-mods', 'recommended',
            'similar-addons', 'you-might-like', 'more-from-author'
        ]

        # أنماط للصور المفضلة
        preferred_patterns = [
            'forgecdn.net', 'media.forgecdn.net', 'r2.mcpedl.com',
            'screenshot', 'gallery', 'preview', 'showcase'
        ]

        # إذا لم نجد أي صور بعد الفلترة، نحتفظ بالصور الأصلية
        all_filtered = []

        for url in image_urls:
            url_lower = url.lower()

            # تجنب الصور غير المرغوبة
            should_avoid = any(pattern in url_lower for pattern in avoid_patterns)
            if should_avoid:
                # نحتفظ بها في قائمة منفصلة في حالة عدم وجود صور أخرى
                all_filtered.append(url)
                continue

            # تحديد أولوية الصورة
            source_type = image_sources.get(url, 'general')
            is_preferred = any(pattern in url_lower for pattern in preferred_patterns)
            is_high_priority = source_type in ['featured', 'thumbnail', 'main', 'forgecdn']

            # إضافة الصورة مع ترتيب حسب الأولوية
            if is_high_priority or is_preferred:
                filtered.insert(0, url)  # إضافة في المقدمة
            else:
                filtered.append(url)

        # إذا لم نجد أي صور بعد الفلترة، نستخدم جميع الصور المتاحة
        if not filtered and all_filtered:
            print("⚠️ لم يتم العثور على صور بعد الفلترة. استخدام جميع الصور المتاحة.")
            # ابحث عن صور في الصفحة الرئيسية للمود
            for url in all_filtered:
                if 'mcpedl.com' in url and not any(p in url.lower() for p in ['empty.png', 'arrow.svg']):
                    filtered.append(url)
            
            # إذا لم نجد أي صور مناسبة، استخدم أي صورة متاحة
            if not filtered:
                filtered = all_filtered

        return filtered

    def _prioritize_images(self, image_urls: List[str], image_sources: Dict[str, str]) -> List[str]:
        """ترتيب الصور حسب الأولوية"""
        # تصنيف الصور حسب المصدر
        priority_order = {
            'featured': 100,
            'thumbnail': 90,
            'main': 85,
            'forgecdn': 80,
            'gallery': 70,
            'mod_gallery': 70,
            'screenshots': 65,
            'wp_gallery': 60,
            'mcpedl': 55,
            'content': 40,
            'post_content': 35,
            'general': 20
        }

        # ترتيب الصور
        def get_priority(url):
            source = image_sources.get(url, 'general')
            base_priority = priority_order.get(source, 0)

            # تعديل الأولوية بناءً على خصائص الرابط
            url_lower = url.lower()
            if 'forgecdn' in url_lower:
                base_priority += 20
            elif 'screenshot' in url_lower or 'preview' in url_lower:
                base_priority += 10
            elif 'gallery' in url_lower:
                base_priority += 5

            return base_priority

        sorted_images = sorted(image_urls, key=get_priority, reverse=True)
        return sorted_images

    def _pre_filter_images(self, image_urls: List[str]) -> List[str]:
        """فلترة أولية لإزالة الصور غير المرغوبة"""
        filtered = []

        for url in image_urls:
            url_lower = url.lower()

            # تجنب الصور الواضحة غير المرغوبة
            should_avoid = False

            # أنماط محسنة للصور غير المرغوبة
            avoid_patterns_enhanced = [
                'empty.png',
                'arrow.svg',
                'icon.png',
                'logo.png',
                'favicon',
                'gravatar',
                'avatar',
                'profile',
                'comment',
                'user-image',
                'author-photo',
                'advertisement',
                'ad-',
                '/ads/',
                'sponsor',
                'related-posts',
                'suggested-mods',
                'other-mods',
                'recommended',
                'similar-addons',
                'you-might-like',
                'more-from-author'
            ]

            for pattern in avoid_patterns_enhanced:
                if pattern in url_lower:
                    should_avoid = True
                    print(f"❌ تم رفض صورة غير مرغوبة: {url[:50]}... (سبب: {pattern})")
                    break

            if should_avoid:
                continue

            # تفضيل صور ForgeCD والمصادر الموثوقة
            if any(trusted in url_lower for trusted in ['forgecdn.net', 'r2.mcpedl.com/users', 'media.forgecdn.net']):
                filtered.insert(0, url)  # إضافة في المقدمة
                print(f"✅ تم قبول صورة موثوقة: {url[:50]}...")
            elif url_lower.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
                filtered.append(url)
                print(f"✅ تم قبول صورة: {url[:50]}...")

        return filtered

    def _gemini_filter_images(self, image_urls: List[str], mod_name: str) -> List[str]:
        """فلترة الصور باستخدام Gemini AI"""
        if not self.gemini_model or len(image_urls) <= 3:
            return image_urls
        
        try:
            # تحضير الصور للتحليل (أول 10 صور فقط لتوفير الوقت)
            images_to_analyze = image_urls[:10]
            image_data_list = []
            
            for i, url in enumerate(images_to_analyze):
                try:
                    response = requests.get(url, timeout=10)
                    if response.status_code == 200 and len(response.content) > 1000:
                        image_data = base64.b64encode(response.content).decode('utf-8')
                        image_data_list.append(image_data)
                    else:
                        image_data_list.append(None)
                except:
                    image_data_list.append(None)
            
            # إنشاء prompt للذكاء الاصطناعي
            prompt = f"""أنت خبير في تحليل صور مودات Minecraft. 

اسم المود: {mod_name}

أمامك {len(images_to_analyze)} صورة. مهمتك:
1. اختيار الصور المتعلقة بالمود "{mod_name}" فقط
2. تجنب صور المودات الأخرى المقترحة
3. تجنب صور المستخدمين أو التعليقات
4. تجنب صور الإعلانات أو المحتوى غير المرتبط
5. اختيار 3-6 صور كحد أقصى

أعد قائمة بأرقام الصور المختارة (تبدأ من 0) في شكل JSON:
{{"selected_indices": [0, 1, 2]}}

اختر فقط الصور الأكثر صلة بالمود."""

            # إرسال الطلب إلى Gemini
            content_parts = [prompt]
            for i, image_data in enumerate(image_data_list):
                if image_data:
                    content_parts.append({
                        "mime_type": "image/jpeg",
                        "data": image_data
                    })
            
            response = self.gemini_model.generate_content(content_parts)
            
            if response and response.text:
                # تحليل الاستجابة
                try:
                    result = json.loads(response.text.strip())
                    selected_indices = result.get('selected_indices', [])
                    
                    selected_images = []
                    for idx in selected_indices:
                        if 0 <= idx < len(images_to_analyze):
                            selected_images.append(images_to_analyze[idx])
                    
                    return selected_images
                    
                except json.JSONDecodeError:
                    print("⚠️ خطأ في تحليل استجابة Gemini")
                    
        except Exception as e:
            print(f"⚠️ خطأ في فلترة Gemini: {e}")
        
        # في حالة الفشل، إرجاع أول 5 صور
        return image_urls[:5]

    def _extract_social_media_smart(self, html_content: str, page_url: str, mod_name: str) -> Dict[str, Any]:
        """استخراج مواقع التواصل الاجتماعي بذكاء"""
        # استخراج جميع الروابط المحتملة
        all_social_links = self._extract_all_social_links(html_content)
        
        # فلترة روابط المشاركة الخاصة بـ MCPEDL
        if self.enhancements:
            # استخدام الفلترة المتقدمة
            creator_links = self.enhancements.advanced_social_filter(all_social_links, mod_name)
            print(f"📋 تم العثور على {len(creator_links)} رابط تواصل محتمل (فلترة متقدمة)")

            return {
                'creator_social_channels': creator_links,
                'creator_contact_info': ', '.join(creator_links)
            }
        else:
            # استخدام الفلترة الأساسية
            filtered_links = self._filter_mcpedl_share_links(all_social_links)

            print(f"📋 تم العثور على {len(filtered_links)} رابط تواصل محتمل")

            # تحليل ذكي باستخدام Gemini إذا كان متوفراً
            if self.gemini_model and filtered_links:
                creator_links = self._gemini_analyze_social_links(filtered_links, mod_name)
                if creator_links:
                    return {
                        'creator_social_channels': creator_links,
                        'creator_contact_info': ', '.join(creator_links)
                    }

            # اختيار احتياطي بناءً على الأنماط
            fallback_links = self._fallback_social_selection(filtered_links)
        
        return {
            'creator_social_channels': fallback_links,
            'creator_contact_info': ', '.join(fallback_links) if fallback_links else ''
        }

    def _extract_all_social_links(self, html_content: str) -> List[Dict[str, str]]:
        """استخراج جميع روابط التواصل الاجتماعي من الصفحة"""
        soup = BeautifulSoup(html_content, 'html.parser')
        social_links = []

        # منصات التواصل الاجتماعي المعروفة
        social_platforms = {
            'youtube.com': 'YouTube',
            'youtu.be': 'YouTube',
            'twitter.com': 'Twitter',
            'x.com': 'Twitter',
            'facebook.com': 'Facebook',
            'instagram.com': 'Instagram',
            'discord.gg': 'Discord',
            'discord.com': 'Discord',
            'tiktok.com': 'TikTok',
            'twitch.tv': 'Twitch',
            'reddit.com': 'Reddit',
            'telegram.me': 'Telegram',
            't.me': 'Telegram',
            'github.com': 'GitHub',
            'patreon.com': 'Patreon'
        }

        # البحث في جميع الروابط
        for link in soup.find_all('a', href=True):
            href = link.get('href', '').strip()
            if not href or href.startswith('#'):
                continue

            # التحقق من المنصات الاجتماعية
            for domain, platform in social_platforms.items():
                if domain in href.lower():
                    social_links.append({
                        'url': href,
                        'platform': platform,
                        'domain': domain,
                        'link_text': link.get_text(strip=True),
                        'context': self._get_link_context(link),
                        'parent_element': link.parent.name if link.parent else ''
                    })
                    break

        # البحث في النص عن إشارات اجتماعية
        text_mentions = self._extract_social_mentions_from_text(html_content)
        social_links.extend(text_mentions)

        return social_links

    def _get_link_context(self, link_element) -> str:
        """الحصول على سياق الرابط"""
        try:
            # البحث عن النص المحيط
            parent = link_element.parent
            if parent:
                context_text = parent.get_text(strip=True)
                return context_text[:100]  # أول 100 حرف
            return ''
        except:
            return ''

    def _extract_social_mentions_from_text(self, html_content: str) -> List[Dict[str, str]]:
        """استخراج إشارات التواصل الاجتماعي من النص"""
        mentions = []

        # أنماط البحث عن الإشارات
        patterns = {
            'Discord': r'discord\.gg/[a-zA-Z0-9]+',
            'YouTube': r'youtube\.com/[@\w]+',
            'Twitter': r'@\w{1,15}(?=\s|$|[^\w])',
            'Telegram': r't\.me/\w+'
        }

        for platform, pattern in patterns.items():
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if platform == 'Twitter':
                    url = f"https://twitter.com/{match[1:]}"  # إزالة @
                elif not match.startswith('http'):
                    url = f"https://{match}"
                else:
                    url = match

                mentions.append({
                    'url': url,
                    'platform': platform,
                    'domain': urlparse(url).netloc,
                    'link_text': match,
                    'context': 'extracted from text',
                    'parent_element': ''
                })

        return mentions

    def _filter_mcpedl_share_links(self, social_links: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """فلترة روابط المشاركة الخاصة بـ MCPEDL"""
        filtered = []

        for link in social_links:
            url = link['url'].lower()

            # تجنب روابط المشاركة الخاصة بـ MCPEDL
            is_share_link = False
            for pattern in self.mcpedl_share_patterns:
                if pattern in url:
                    is_share_link = True
                    break

            # تجنب روابط المشاركة العامة
            share_indicators = [
                'sharer.php',
                'intent/tweet',
                'share?url=',
                'submit?url=',
                'pin/create',
                '/share/',
                'share.php'
            ]

            for indicator in share_indicators:
                if indicator in url:
                    is_share_link = True
                    break

            if not is_share_link:
                filtered.append(link)

        return filtered

    def _gemini_analyze_social_links(self, social_links: List[Dict[str, str]], mod_name: str) -> List[str]:
        """تحليل روابط التواصل الاجتماعي باستخدام Gemini"""
        if not self.gemini_model or not social_links:
            return []

        try:
            # تحضير البيانات للتحليل
            links_info = []
            for i, link in enumerate(social_links):
                links_info.append({
                    'index': i,
                    'url': link['url'],
                    'platform': link['platform'],
                    'text': link['link_text'],
                    'context': link['context']
                })

            prompt = f"""أنت خبير في تحليل مواقع التواصل الاجتماعي لمطوري مودات Minecraft.

اسم المود: {mod_name}

أمامك قائمة بروابط التواصل الاجتماعي من صفحة المود. مهمتك:
1. اختيار الروابط التي تخص مطور المود فقط
2. تجنب روابط المشاركة العامة لـ MCPEDL
3. تجنب روابط المودات الأخرى أو المطورين الآخرين
4. التركيز على الروابط الشخصية للمطور

الروابط المتاحة:
{json.dumps(links_info, indent=2, ensure_ascii=False)}

أعد قائمة بأرقام الروابط المختارة في شكل JSON:
{{"selected_indices": [0, 1, 2]}}

اختر فقط الروابط المتعلقة بمطور المود."""

            response = self.gemini_model.generate_content(prompt)

            if response and response.text:
                try:
                    result = json.loads(response.text.strip())
                    selected_indices = result.get('selected_indices', [])

                    selected_links = []
                    for idx in selected_indices:
                        if 0 <= idx < len(social_links):
                            selected_links.append(social_links[idx]['url'])

                    return selected_links

                except json.JSONDecodeError:
                    print("⚠️ خطأ في تحليل استجابة Gemini للروابط الاجتماعية")

        except Exception as e:
            print(f"⚠️ خطأ في تحليل Gemini للروابط: {e}")

        return []

    def _fallback_social_selection(self, social_links: List[Dict[str, str]]) -> List[str]:
        """اختيار احتياطي للروابط الاجتماعية"""
        selected = []

        # أولوية المنصات
        platform_priority = ['YouTube', 'Discord', 'Twitter', 'GitHub', 'Patreon']

        for platform in platform_priority:
            for link in social_links:
                if link['platform'] == platform and link['url'] not in selected:
                    # فحص إضافي للتأكد من أنه ليس رابط مشاركة
                    if not any(indicator in link['url'].lower()
                             for indicator in ['share', 'sharer', 'intent']):
                        selected.append(link['url'])
                        break

        return selected[:5]  # حد أقصى 5 روابط

    def _extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        title_selectors = self.selectors['title'].split(', ')

        for selector in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = element.get_text(strip=True)
                if title and len(title) > 2:
                    # تنظيف العنوان من الكلمات الإضافية
                    title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '').strip()
                    return title

        # البحث في meta tags
        meta_title = soup.find('meta', {'property': 'og:title'})
        if meta_title and meta_title.get('content'):
            title = meta_title['content'].strip()
            title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '').strip()
            if title:
                return title

        # البحث في title tag
        title_tag = soup.find('title')
        if title_tag:
            title = title_tag.get_text(strip=True)
            title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '').strip()
            if title:
                return title

        return ""

    def _extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        # محددات محسنة للوصف
        desc_selectors = [
            '.entry-content',
            '.post-content',
            '.mod-description',
            '.addon-description',
            '.content',
            '.description',
            '.post-body',
            'article .content',
            'article .entry-content',
            '.single-post .content',
            '.post .content',
            'main .content',
            '.main-content',
            '.post-text',
            '.entry-text'
        ]

        print("🔍 البحث عن الوصف...")

        for selector in desc_selectors:
            elements = soup.select(selector)
            for element in elements:
                if element:
                    print(f"   جاري فحص: {selector}")

                    # إنشاء نسخة للتعديل
                    element_copy = element.__copy__()

                    # إزالة العناصر غير المرغوبة
                    for unwanted in element_copy.find_all(['script', 'style', 'nav', 'footer', 'aside', 'header']):
                        unwanted.decompose()

                    # إزالة الإعلانات والمحتوى غير المرغوب
                    for unwanted_class in element_copy.find_all(class_=['ad', 'advertisement', 'related-posts', 'comments', 'social-share', 'share-buttons']):
                        unwanted_class.decompose()

                    # إزالة روابط التحميل والتنقل
                    for unwanted_link in element_copy.find_all('a', href=True):
                        href = unwanted_link.get('href', '').lower()
                        if any(pattern in href for pattern in ['download', 'install', 'guide', 'tutorial']):
                            unwanted_link.decompose()

                    description = element_copy.get_text(strip=True)
                    print(f"   طول النص المستخرج: {len(description)}")

                    if len(description) > 100:  # وصف معقول
                        # تنظيف الوصف بشكل شامل
                        cleaned_description = self._clean_description_text(description)
                        print(f"   طول النص بعد التنظيف: {len(cleaned_description)}")

                        if len(cleaned_description) > 50:
                            print(f"   ✅ تم العثور على وصف من: {selector}")
                            return cleaned_description

        print("   ⚠️ لم يتم العثور على وصف في المحددات الأساسية")

        # البحث في meta description
        print("🔍 البحث في meta description...")
        meta_desc = soup.find('meta', {'name': 'description'})
        if meta_desc and meta_desc.get('content'):
            description = meta_desc['content'].strip()
            print(f"   طول meta description: {len(description)}")
            if len(description) > 50:
                print("   ✅ تم العثور على وصف من meta description")
                return description

        # البحث في og:description
        print("🔍 البحث في og:description...")
        og_desc = soup.find('meta', {'property': 'og:description'})
        if og_desc and og_desc.get('content'):
            description = og_desc['content'].strip()
            print(f"   طول og:description: {len(description)}")
            if len(description) > 50:
                print("   ✅ تم العثور على وصف من og:description")
                return description

        # البحث في النص العام للصفحة كحل أخير
        print("🔍 البحث في النص العام للصفحة...")
        body_text = soup.get_text()
        if len(body_text) > 1000:
            # استخراج فقرة من وسط النص
            lines = body_text.split('\n')
            for line in lines:
                line = line.strip()
                if len(line) > 100 and len(line) < 1000:
                    # فحص إذا كانت الفقرة تحتوي على كلمات مفيدة
                    if any(word in line.lower() for word in ['mod', 'addon', 'texture', 'pack', 'minecraft', 'block', 'item']):
                        cleaned = self._clean_description_text(line)
                        if len(cleaned) > 50:
                            print(f"   ✅ تم استخراج وصف من النص العام: {len(cleaned)} حرف")
                            return cleaned

        print("   ❌ لم يتم العثور على أي وصف")
        return ""

    def _clean_description_text(self, description: str) -> str:
        """
        تنظيف محسن لنص الوصف من عناصر HTML غير المرغوبة.
        يزيل أكواد JavaScript و CSS، الروابط غير المرغوبة، والصور الصغيرة.
        """
        if not description:
            return ""

        # إذا كان النص عادي (بدون HTML)، قم بتنظيفه مباشرة
        if '<' not in description:
            return self._clean_plain_text(description)

        soup = BeautifulSoup(description, 'html.parser')

        # إزالة العناصر غير المرغوبة تماماً
        unwanted_elements = [
            'script', 'style', 'nav', 'footer', 'aside', 'header',
            'iframe', 'embed', 'object', 'applet', 'form', 'input',
            'button', 'select', 'textarea', 'noscript'
        ]
        for element_type in unwanted_elements:
            for element in soup.find_all(element_type):
                element.decompose()

        # إزالة العناصر بفئات غير مرغوبة
        unwanted_classes = [
            'ad', 'advertisement', 'ads', 'sponsor', 'promo',
            'related-posts', 'comments', 'social-share', 'share-buttons',
            'navigation', 'nav', 'sidebar', 'footer', 'header',
            'breadcrumb', 'pagination', 'tags', 'categories'
        ]
        for class_name in unwanted_classes:
            for element in soup.find_all(class_=lambda x: x and class_name in ' '.join(x).lower()):
                element.decompose()

        # تنظيف الروابط
        for a_tag in soup.find_all('a'):
            href = a_tag.get('href', '').lower()
            text = a_tag.get_text(strip=True)

            # إزالة الروابط الفارغة أو غير المرغوبة
            should_remove = (
                not text or  # روابط بدون نص
                'download' in href or 'mediafire' in href or 'google.com/drive' in href or
                'bit.ly' in href or 'tinyurl.com' in href or  # روابط مختصرة
                any(platform in href for platform in [
                    'youtube.com', 'twitter.com', 'facebook.com', 'discord.gg',
                    'instagram.com', 'tiktok.com', 'reddit.com'
                ]) or
                href.startswith('#') or  # روابط داخلية
                'javascript:' in href  # روابط JavaScript
            )

            if should_remove:
                a_tag.decompose()
            else:
                # استبدال الرابط بالنص فقط
                a_tag.replace_with(text)

        # تنظيف الصور
        for img_tag in soup.find_all('img'):
            src = img_tag.get('src', '').lower()
            alt = img_tag.get('alt', '').lower()
            width = img_tag.get('width')
            height = img_tag.get('height')

            # تحديد الصور غير المرغوبة
            is_small = False
            try:
                if width and int(width) < 50:
                    is_small = True
                if height and int(height) < 50:
                    is_small = True
            except (ValueError, TypeError):
                pass

            is_unwanted = any(pattern in src or pattern in alt for pattern in [
                'icon', 'logo', 'ad', 'advertisement', 'sponsor',
                'gravatar', 'avatar', 'profile', 'user', 'comment',
                'emoji', 'smiley', 'emoticon'
            ])

            if is_small or is_unwanted:
                img_tag.decompose()
            else:
                # استبدال الصورة بنص alt إذا كان متوفراً
                if alt and len(alt) > 3:
                    img_tag.replace_with(f"[صورة: {alt}]")
                else:
                    img_tag.decompose()

        # الحصول على النص النظيف
        clean_text = soup.get_text(separator='\n', strip=True)

        # تنظيف النص النهائي
        return self._clean_plain_text(clean_text)

    def _clean_plain_text(self, text: str) -> str:
        """تنظيف النص العادي من التنسيقات غير المرغوبة"""
        if not text:
            return ""

        # فك تشفير كيانات HTML
        text = html.unescape(text)

        # تحويل جميع الأسطر الجديدة إلى مسافات (لإزالة الفقرات تماماً)
        text = re.sub(r'\n', ' ', text)
        
        # إزالة الأسطر الفارغة المتعددة
        text = re.sub(r'\s*\n\s*\n\s*', ' ', text)

        # إزالة المسافات الزائدة
        text = re.sub(r'[ \t]+', ' ', text)

        # إزالة الأنماط غير المرغوبة
        unwanted_patterns = [
            r'^\s*[\*\-\•]\s*',  # نقاط في بداية الأسطر
            r'\[.*?\]',  # نصوص بين أقواس مربعة
            r'Click here.*?$',  # روابط "اضغط هنا"
            r'Download.*?$',  # روابط التحميل
            r'More info.*?$',  # روابط "معلومات أكثر"
            r'Read more.*?$',  # روابط "اقرأ المزيد"
        ]

        for pattern in unwanted_patterns:
            text = re.sub(pattern, '', text, flags=re.MULTILINE | re.IGNORECASE)

        # تنظيف نهائي - تحويل إلى نص متواصل بدون فقرات
        text = re.sub(r'\s+', ' ', text).strip()
        
        # إزالة أي أسطر جديدة متبقية
        text = re.sub(r'\n', ' ', text)

        return text

    def _extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        cat_selectors = self.selectors['category'].split(', ')
        for selector in cat_selectors:
            element = soup.select_one(selector)
            if element:
                category = element.get_text(strip=True)
                if category:
                    # تحويل إلى فئات معيارية
                    category_lower = category.lower()
                    if any(word in category_lower for word in ['shader', 'shaders']):
                        return "Shaders"
                    elif any(word in category_lower for word in ['texture', 'resource', 'pack']):
                        return "Texture Pack"
                    else:
                        return "Addons"
        return "Addons"

    def _extract_version(self, soup: BeautifulSoup) -> str:
        """
        استخراج إصدارات Minecraft PE المدعومة من نص الصفحة.
        تبحث عن أنماط مثل "Minecraft PE 1.x.x" أو "MCPE vX.Y.Z".
        وتستخرج الإصدارات من قسم "Supported Minecraft versions" في أسفل الصفحة.
        """
        print("🔍 البحث عن إصدار Minecraft PE...")
        
        # 1. البحث أولاً في قسم "Supported Minecraft versions" (أعلى أولوية)
        versions_section = soup.select_one('.versions, div.versions[data-v-40777755]')
        if versions_section:
            print("   ✅ تم العثور على قسم 'Supported Minecraft versions'")
            version_items = versions_section.select('li.versions-item a')
            if version_items:
                supported_versions = []
                for item in version_items:
                    version_text = item.get_text(strip=True)
                    if version_text and self._is_valid_version(version_text):
                        supported_versions.append(version_text)
                        print(f"   ✅ وجد إصدار في قسم Supported Versions: {version_text}")
                
                if supported_versions:
                    # إزالة التكرارات وترتيب حسب الأولوية
                    unique_versions = list(dict.fromkeys(supported_versions))
                    best_version = self._select_best_version(unique_versions)
                    print(f"✅ تم اختيار الإصدار من قسم Supported Versions: {best_version}")
                    return best_version
                    
                    # يمكن أيضًا إرجاع جميع الإصدارات المدعومة كسلسلة مفصولة بفواصل
                    # return ", ".join(unique_versions)

        # 2. البحث في النص العام (إذا لم يتم العثور على قسم الإصدارات المدعومة)
        # أنماط محسنة للبحث عن الإصدارات
        version_patterns = [
            # أنماط أساسية محسنة
            r'(?:Minecraft\s*(?:PE|Bedrock)|MCPE|MCBE|Version|Supports?)\s*[:\s]*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',
            r'v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)\s*(?:for|compatible\s*with|supports?)\s*(?:Minecraft\s*)?(?:PE|Bedrock|MCPE|MCBE)',
            r'(?:Compatible|Works)\s*(?:with|on)\s*(?:Minecraft\s*)?(?:PE|Bedrock)\s*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',
            r'(?:Requires|Needs)\s*(?:Minecraft\s*)?(?:PE|Bedrock)\s*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',
            r'(?:Tested\s*(?:on|with)|Working\s*(?:on|with))\s*(?:Minecraft\s*)?(?:PE|Bedrock)\s*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',

            # أنماط من العناوين والوصف
            r'(?:for|compatible)\s*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)\s*(?:and\s*(?:above|higher|up))?',
            r'v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)\s*(?:\+|and\s*(?:above|higher|up|later))',
            r'(?:Version|Ver\.?)\s*v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',

            # أنماط من meta tags
            r'minecraft[_\-]?(?:pe|bedrock)[_\-]?version["\']?\s*[:=]\s*["\']?v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',
            r'game[_\-]?version["\']?\s*[:=]\s*["\']?v?(\d+\.\d+(?:\.\d+)?(?:\.\d+)?)',
        ]

        found_versions = []

        # البحث في عناصر محددة بأولوية
        search_selectors = [
            # عناصر عالية الأولوية
            '.version-info', '.mod-version', '.addon-version', '.compatibility',
            '.requirements', '.system-requirements', '.game-version',

            # عناصر متوسطة الأولوية
            '.entry-content', '.post-content', '.mod-description', '.description',

            # عناصر منخفضة الأولوية
            'h1', 'h2', 'h3', '.title', '.post-title',

            # البحث العام كحل أخير
            'body'
        ]

        for selector in search_selectors:
            elements = soup.select(selector)
            for element in elements:
                if element:
                    text = element.get_text(separator=' ', strip=True)

                    # البحث باستخدام جميع الأنماط
                    for pattern in version_patterns:
                        matches = re.finditer(pattern, text, re.IGNORECASE)
                        for match in matches:
                            version = match.group(1).strip()
                            if version and self._is_valid_version(version):
                                found_versions.append(version)
                                print(f"   ✅ وجد إصدار: {version} في {selector}")

        # البحث في meta tags
        meta_tags = soup.find_all('meta')
        for meta in meta_tags:
            content = meta.get('content', '')
            name = meta.get('name', '')
            property_attr = meta.get('property', '')

            search_text = f"{name} {property_attr} {content}".lower()
            if any(keyword in search_text for keyword in ['version', 'minecraft', 'mcpe', 'bedrock']):
                for pattern in version_patterns:
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        version = match.group(1).strip()
                        if version and self._is_valid_version(version):
                            found_versions.append(version)
                            print(f"   ✅ وجد إصدار في meta: {version}")

        # إرجاع أفضل إصدار
        if found_versions:
            # إزالة التكرارات وترتيب حسب الأولوية
            unique_versions = list(dict.fromkeys(found_versions))
            best_version = self._select_best_version(unique_versions)
            print(f"✅ تم اختيار الإصدار: {best_version}")
            return best_version

        print("⚠️ لم يتم العثور على إصدار محدد")
        return ""

    def _is_valid_version(self, version: str) -> bool:
        """التحقق من صحة رقم الإصدار"""
        if not version:
            return False

        # التحقق من تنسيق الإصدار
        version_pattern = r'^\d+\.\d+(?:\.\d+)?(?:\.\d+)?$'
        if not re.match(version_pattern, version):
            return False

        # التحقق من منطقية الإصدار (Minecraft PE versions)
        parts = version.split('.')
        major = int(parts[0])
        minor = int(parts[1])

        # إصدارات Minecraft PE تبدأ من 0.1.0 وتصل إلى 1.x.x
        if major == 0 and minor >= 1:
            return True
        elif major == 1 and minor >= 0:
            return True
        elif major > 1:  # إصدارات مستقبلية محتملة
            return True

        return False

    def _select_best_version(self, versions: List[str]) -> str:
        """اختيار أفضل إصدار من القائمة"""
        if not versions:
            return ""

        if len(versions) == 1:
            return versions[0]

        # ترتيب الإصدارات حسب الأولوية (الأحدث أولاً)
        def version_key(v):
            parts = [int(x) for x in v.split('.')]
            # إضافة أصفار للإصدارات القصيرة
            while len(parts) < 4:
                parts.append(0)
            return tuple(parts)

        sorted_versions = sorted(versions, key=version_key, reverse=True)
        return sorted_versions[0]

    def _extract_creator_name(self, soup: BeautifulSoup) -> str:
        """استخراج اسم منشئ المود"""
        # محاولة استخراج اسم المطور من عدة أماكن محتملة
        creator_selectors = [
            '.author-info .author-name',
            '.post-author .author-name',
            '.creator-name',
            '.by-author',
            '.author-link',
            '.post-meta .author',
            '.entry-meta .author',
            'span.author',
            '.vcard .fn',
            '[rel="author"]'
        ]

        for selector in creator_selectors:
            element = soup.select_one(selector)
            if element:
                creator_name = element.get_text(strip=True)
                if creator_name and len(creator_name) > 1:
                    # تنظيف الاسم من الكلمات الإضافية
                    creator_name = creator_name.replace('By ', '').replace('by ', '').replace('Author: ', '').strip()
                    return creator_name

        # البحث في meta tags
        meta_author = soup.find('meta', {'name': 'author'})
        if meta_author and meta_author.get('content'):
            return meta_author['content'].strip()

        # البحث في structured data
        json_ld_scripts = soup.find_all('script', {'type': 'application/ld+json'})
        for script in json_ld_scripts:
            try:
                data = json.loads(script.string)
                if isinstance(data, dict) and 'author' in data:
                    author = data['author']
                    if isinstance(author, dict) and 'name' in author:
                        return author['name']
                    elif isinstance(author, str):
                        return author
            except:
                continue

        return ""

    def _generate_additional_descriptions(self, mod_data: Dict[str, Any]) -> Dict[str, str]:
        """توليد الأوصاف الإضافية باستخدام Gemini"""
        additional_descriptions = {}

        if not self.gemini_model:
            return additional_descriptions

        mod_name = mod_data.get('name', '')
        description = mod_data.get('description', '')
        category = mod_data.get('category', 'Addons')

        try:
            # إنشاء وصف إنجليزي مفصل وشامل
            print("🔄 إنشاء وصف إنجليزي مفصل وشامل...")
            english_prompt = f"""Create a comprehensive and detailed English description for this Minecraft mod:

Mod Name: {mod_name}
Category: {category}
Basic Information: {description}

Requirements for the description:
1. Write a complete, detailed description (400-600 words)
2. Use a simple, classic format without headings or sections
3. Write in a continuous flow WITHOUT paragraph breaks
4. Use engaging and professional language
5. Make it informative and helpful for players
6. Include specific details about what the mod does
7. Don't mention other mod versions or compatibility info
8. DO NOT use headings, bullet points, or numbered lists
9. Write in a natural, flowing style like a product description
10. Focus on describing features and benefits in a cohesive way
11. IMPORTANT: Do NOT include any paragraph breaks or line breaks in the text
12. The entire description should be one continuous paragraph

Write the comprehensive English description:"""

            english_response = self.gemini_model.generate_content(english_prompt)
            if english_response and english_response.text:
                comprehensive_description = english_response.text.strip()
                # إزالة أي أسطر جديدة في الوصف
                comprehensive_description = re.sub(r'\n', ' ', comprehensive_description)
                comprehensive_description = re.sub(r'\s+', ' ', comprehensive_description).strip()
                
                if len(comprehensive_description) > 500:  # تأكد من أنه مفصل
                    description = comprehensive_description
                    # تحديث البيانات الأصلية
                    mod_data['description'] = description
                    # إضافة الوصف الإنجليزي أيضاً كحقل منفصل للتوافق (بجميع الأسماء المحتملة)
                    additional_descriptions['description_english'] = description
                    additional_descriptions['english_description'] = description
                    additional_descriptions['desc_en'] = description
                    # تحديث mod_data مباشرة أيضاً
                    mod_data['description_english'] = description
                    mod_data['english_description'] = description
                    mod_data['desc_en'] = description
                    print(f"✅ تم إنشاء وصف إنجليزي شامل: {len(description)} حرف")
                    print(f"✅ تم تحديث الوصف الإنجليزي في mod_data مباشرة")
                else:
                    print("⚠️ الوصف المولد قصير، سيتم الاحتفاظ بالأصلي")
                    # حتى مع الوصف القصير، نضيف الوصف الإنجليزي كحقل منفصل (بجميع الأسماء المحتملة)
                    original_desc = mod_data.get('description', '')
                    additional_descriptions['description_english'] = original_desc
                    additional_descriptions['english_description'] = original_desc
                    additional_descriptions['desc_en'] = original_desc
                    # تحديث mod_data مباشرة أيضاً
                    mod_data['description_english'] = original_desc
                    mod_data['english_description'] = original_desc
                    mod_data['desc_en'] = original_desc
                    print(f"✅ تم تحديث الوصف الإنجليزي في mod_data مباشرة (الوصف الأصلي)")

            # توليد الوصف العربي
            print("🔄 توليد الوصف العربي...")
            arabic_prompt = f"""اكتب وصفاً مفصلاً باللغة العربية لهذا المود، مع الحفاظ على نفس المحتوى والتفاصيل الموجودة في الوصف الإنجليزي:

اسم المود: {mod_name}
الفئة: {category}
الوصف الإنجليزي المرجعي: {description}

متطلبات الوصف العربي:
1. نفس المحتوى والمعلومات الموجودة في الوصف الإنجليزي
2. نفس الطول والتفصيل (400-600 كلمة)
3. استخدام تنسيق بسيط وكلاسيكي بدون عناوين أو أقسام
4. الكتابة بتدفق مستمر بدون فواصل فقرات
5. استخدام لغة عربية واضحة وجذابة
6. ترجمة دقيقة للمصطلحات التقنية
7. عدم استخدام العناوين أو النقاط أو القوائم المرقمة
8. الكتابة بأسلوب طبيعي ومتدفق مثل وصف المنتج
9. التركيز على وصف الميزات والفوائد بطريقة متماسكة
10. عدم إضافة أو حذف معلومات مهمة
11. مهم جداً: لا تستخدم أي فواصل فقرات أو أسطر جديدة في النص
12. يجب أن يكون الوصف بأكمله فقرة واحدة متواصلة

اكتب الوصف العربي المفصل والشامل:"""

            arabic_response = self.gemini_model.generate_content(arabic_prompt)
            if arabic_response and arabic_response.text:
                arabic_description = arabic_response.text.strip()
                # إزالة أي أسطر جديدة في الوصف العربي
                arabic_description = re.sub(r'\n', ' ', arabic_description)
                arabic_description = re.sub(r'\s+', ' ', arabic_description).strip()

                # فحص جودة الوصف العربي
                if len(arabic_description) > 500:  # تأكد من أنه مفصل
                    additional_descriptions['description_arabic'] = arabic_description
                    print(f"✅ تم توليد الوصف العربي المفصل: {len(arabic_description)} حرف")

                    # فحص التطابق في الطول
                    english_length = len(description)
                    arabic_length = len(arabic_description)
                    ratio = arabic_length / english_length if english_length > 0 else 0

                    if 0.7 <= ratio <= 1.5:  # نسبة معقولة
                        print(f"✅ نسبة الطول متوازنة: عربي {arabic_length} / إنجليزي {english_length}")
                    else:
                        print(f"⚠️ نسبة الطول غير متوازنة: عربي {arabic_length} / إنجليزي {english_length}")
                else:
                    print("⚠️ الوصف العربي المولد قصير جداً، سيتم إعادة المحاولة...")

                    # محاولة ثانية بطلب أكثر تفصيلاً
                    retry_prompt = f"""اكتب وصفاً عربياً مفصلاً لهذا المود (يجب أن يكون 400-600 كلمة) بتنسيق كلاسيكي بسيط:

اسم المود: {mod_name}
المعلومات الأساسية: {description[:200]}...

يجب أن يتضمن الوصف:
- مقدمة طويلة ومفصلة
- شرح مفصل للميزات (على الأقل 5 ميزات)
- تعليمات التثبيت خطوة بخطوة
- كيفية الاستخدام بالتفصيل
- نصائح ومعلومات إضافية للاعبين
- خاتمة تشجيعية

ملاحظات مهمة:
1. اكتب الوصف كفقرة واحدة متواصلة بدون فواصل فقرات
2. لا تستخدم أي أسطر جديدة أو فواصل فقرات
3. يجب أن يكون النص متواصلاً بالكامل

اكتب الوصف العربي الطويل والمفصل كفقرة واحدة متواصلة:"""

                    retry_response = self.gemini_model.generate_content(retry_prompt)
                    if retry_response and retry_response.text:
                        retry_text = retry_response.text.strip()
                        # إزالة أي أسطر جديدة في الوصف
                        retry_text = re.sub(r'\n', ' ', retry_text)
                        retry_text = re.sub(r'\s+', ' ', retry_text).strip()
                        
                        if len(retry_text) > 500:
                            additional_descriptions['description_arabic'] = retry_text
                            print(f"✅ تم توليد الوصف العربي في المحاولة الثانية: {len(retry_text)} حرف")

            # توليد أوصاف التليجرام باستخدام البرومت المخصص
            print("🔄 توليد أوصاف التليجرام باستخدام البرومت المخصص...")
            telegram_prompt = f"""أنشئ وصفين متطابقين في المعنى لهذا المود في ماين كرافت باللغتين العربية والإنجليزية.

معلومات المود:
اسم المود: {mod_name}
الفئة: {category}
الوصف الإنجليزي الحالي: {description}

متطلبات الوصف:
1. الوصفان يجب أن يكونا متطابقين في المعنى والمحتوى
2. طول الوصف: 300-400 حرف لكل لغة
3. استخدام 2-3 إيموجيات مناسبة في كل وصف
4. لهجة طبيعية وبشرية (سعودية للعربية، بريطانية للإنجليزية)
5. للعربي: بداية متنوعة مثل "جبتلكم"، "حبيت أقدم لكم"، "اليوم راح أعرض عليكم"
6. للإنجليزي: بداية مثل "Hey Minecraft fans!", "Check out this amazing mod!", "Here's something cool!"
7. تجنب الكلمات: "تخيل"، "تجربة لعب"، "إضافة"، "عش تجربة"، "عش مغامرة"
8. استخدم اسلوب مراهق يحب ماين كرافت
9. نفس المعلومات والميزات في كلا الوصفين
10. كل وصف يجب أن يكون فقرة واحدة متواصلة بدون فواصل أسطر

أعد فقط JSON بالشكل التالي:
{{
"ar": "الوصف العربي المباشر",
"en": "الوصف الإنجليزي المباشر"
}}"""

            telegram_response = self.gemini_model.generate_content(telegram_prompt)
            if telegram_response and telegram_response.text:
                try:
                    # محاولة تحليل JSON
                    import json
                    telegram_data = json.loads(telegram_response.text.strip())

                    if telegram_data.get('ar'):
                        ar_desc = telegram_data['ar']
                        # إزالة أي أسطر جديدة
                        ar_desc = re.sub(r'\n', ' ', ar_desc)
                        ar_desc = re.sub(r'\s+', ' ', ar_desc).strip()
                        additional_descriptions['telegram_description_ar'] = ar_desc
                        print("✅ تم توليد وصف التليجرام العربي")

                    if telegram_data.get('en'):
                        en_desc = telegram_data['en']
                        # إزالة أي أسطر جديدة
                        en_desc = re.sub(r'\n', ' ', en_desc)
                        en_desc = re.sub(r'\s+', ' ', en_desc).strip()
                        additional_descriptions['telegram_description_en'] = en_desc
                        print("✅ تم توليد وصف التليجرام الإنجليزي")

                except json.JSONDecodeError:
                    print("⚠️ خطأ في تحليل استجابة JSON لأوصاف التليجرام")
                    # محاولة استخراج النصوص يدوياً
                    response_text = telegram_response.text.strip()
                    if '"ar":' in response_text and '"en":' in response_text:
                        try:
                            ar_match = re.search(r'"ar":\s*"([^"]+)"', response_text)
                            en_match = re.search(r'"en":\s*"([^"]+)"', response_text)

                            if ar_match:
                                ar_desc = ar_match.group(1)
                                # إزالة أي أسطر جديدة
                                ar_desc = re.sub(r'\n', ' ', ar_desc)
                                ar_desc = re.sub(r'\s+', ' ', ar_desc).strip()
                                additional_descriptions['telegram_description_ar'] = ar_desc
                                print("✅ تم استخراج وصف التليجرام العربي يدوياً")

                            if en_match:
                                en_desc = en_match.group(1)
                                # إزالة أي أسطر جديدة
                                en_desc = re.sub(r'\n', ' ', en_desc)
                                en_desc = re.sub(r'\s+', ' ', en_desc).strip()
                                additional_descriptions['telegram_description_en'] = en_desc
                                print("✅ تم استخراج وصف التليجرام الإنجليزي يدوياً")
                        except:
                            print("⚠️ فشل في استخراج أوصاف التليجرام")

        except Exception as e:
            print(f"⚠️ خطأ في توليد الأوصاف الإضافية: {e}")

        return additional_descriptions

    def _extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج محسن لمعلومات التحميل"""
        print("🔍 البحث عن معلومات التحميل...")
        download_info = {}

        # البحث عن روابط التحميل بطرق متعددة
        download_links = self._find_download_links(soup, base_url)
        if download_links:
            # اختيار أفضل رابط تحميل
            best_link = self._select_best_download_link(download_links)
            if best_link:
                download_info['download_url'] = best_link['url']
                download_info['download_type'] = best_link['type']
                print(f"✅ تم العثور على رابط التحميل: {best_link['type']}")

        # البحث عن حجم الملف
        file_size = self._extract_file_size(soup)
        if file_size:
            download_info['size'] = file_size
            print(f"✅ تم العثور على حجم الملف: {file_size}")

        # البحث عن معلومات إضافية
        additional_info = self._extract_additional_download_info(soup)
        download_info.update(additional_info)

        return download_info

    def _find_download_links(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """البحث عن جميع روابط التحميل المحتملة"""
        download_links = []

        # محددات محسنة لروابط التحميل
        download_selectors = [
            # محددات عالية الأولوية
            '.download-link', '.btn-download', '.download-button', '.download-btn',
            'a[href*="download"]', 'a[href*="mediafire"]', 'a[href*="drive.google"]',
            'a[href*="dropbox"]', 'a[href*="mega.nz"]', 'a[href*="4shared"]',

            # محددات متوسطة الأولوية
            '.button[href*="download"]', '.btn[href*="download"]',
            'a[class*="download"]', 'a[id*="download"]',

            # محددات منخفضة الأولوية
            'a[href$=".mcaddon"]', 'a[href$=".mcpack"]', 'a[href$=".zip"]',
            'a[href$=".rar"]', 'a[href$=".7z"]'
        ]

        # كلمات مفتاحية للبحث في النصوص
        download_keywords = [
            'download', 'تحميل', 'descargar', 'télécharger',
            'mediafire', 'google drive', 'dropbox', 'mega',
            'mcaddon', 'mcpack', 'behavior pack', 'resource pack'
        ]

        # البحث باستخدام المحددات
        for selector in download_selectors:
            elements = soup.select(selector)
            for element in elements:
                href = element.get('href')
                if href:
                    full_url = urljoin(base_url, href)
                    link_text = element.get_text(strip=True).lower()

                    # تصنيف نوع الرابط
                    link_type = self._classify_download_link(full_url, link_text)
                    if link_type:
                        download_links.append({
                            'url': full_url,
                            'type': link_type,
                            'text': link_text,
                            'priority': self._get_link_priority(link_type, full_url)
                        })

        # البحث في النصوص عن روابط إضافية
        text_links = self._extract_links_from_text(soup, base_url, download_keywords)
        download_links.extend(text_links)

        # إزالة التكرارات
        unique_links = []
        seen_urls = set()
        for link in download_links:
            if link['url'] not in seen_urls:
                unique_links.append(link)
                seen_urls.add(link['url'])

        print(f"📋 تم العثور على {len(unique_links)} رابط تحميل محتمل")
        return unique_links

    def _classify_download_link(self, url: str, text: str) -> Optional[str]:
        """تصنيف نوع رابط التحميل"""
        url_lower = url.lower()
        text_lower = text.lower()

        # روابط مباشرة عالية الجودة
        if any(domain in url_lower for domain in ['forgecdn.net', 'curseforge.com']):
            return 'direct_trusted'

        # روابط خدمات التخزين السحابي
        if 'mediafire.com' in url_lower:
            return 'mediafire'
        elif 'drive.google.com' in url_lower or 'docs.google.com' in url_lower:
            return 'google_drive'
        elif 'dropbox.com' in url_lower:
            return 'dropbox'
        elif 'mega.nz' in url_lower:
            return 'mega'
        elif '4shared.com' in url_lower:
            return 'fourshared'

        # روابط ملفات مباشرة
        if any(ext in url_lower for ext in ['.mcaddon', '.mcpack', '.zip', '.rar', '.7z']):
            return 'direct_file'

        # روابط بناءً على النص
        if any(keyword in text_lower for keyword in ['download', 'تحميل', 'mcaddon', 'mcpack']):
            return 'text_based'

        return None

    def _get_link_priority(self, link_type: str, url: str) -> int:
        """تحديد أولوية الرابط (أعلى رقم = أولوية أعلى)"""
        priority_map = {
            'direct_trusted': 100,
            'direct_file': 90,
            'mediafire': 80,
            'google_drive': 75,
            'dropbox': 70,
            'mega': 65,
            'fourshared': 60,
            'text_based': 50
        }

        base_priority = priority_map.get(link_type, 0)

        # تعديل الأولوية بناءً على خصائص إضافية
        if '.mcaddon' in url.lower():
            base_priority += 10
        elif '.mcpack' in url.lower():
            base_priority += 5

        return base_priority

    def _select_best_download_link(self, download_links: List[Dict[str, str]]) -> Optional[Dict[str, str]]:
        """اختيار أفضل رابط تحميل"""
        if not download_links:
            return None

        # ترتيب حسب الأولوية
        sorted_links = sorted(download_links, key=lambda x: x['priority'], reverse=True)

        # إرجاع الرابط الأفضل
        best_link = sorted_links[0]
        print(f"🎯 تم اختيار أفضل رابط: {best_link['type']} (أولوية: {best_link['priority']})")

        return best_link

    def _extract_links_from_text(self, soup: BeautifulSoup, base_url: str, keywords: List[str]) -> List[Dict[str, str]]:
        """استخراج روابط من النصوص بناءً على الكلمات المفتاحية"""
        text_links = []

        # البحث في النص العام
        page_text = soup.get_text()

        # أنماط للبحث عن الروابط
        url_patterns = [
            r'https?://[^\s<>"\']+',
            r'www\.[^\s<>"\']+',
            r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}[^\s<>"\']*'
        ]

        for pattern in url_patterns:
            matches = re.finditer(pattern, page_text, re.IGNORECASE)
            for match in matches:
                url = match.group().strip('.,!?;')

                # التحقق من وجود كلمات مفتاحية قريبة
                start_pos = max(0, match.start() - 50)
                end_pos = min(len(page_text), match.end() + 50)
                context = page_text[start_pos:end_pos].lower()

                if any(keyword in context for keyword in keywords):
                    if not url.startswith('http'):
                        url = 'https://' + url

                    link_type = self._classify_download_link(url, context)
                    if link_type:
                        text_links.append({
                            'url': url,
                            'type': link_type,
                            'text': context[:30] + '...',
                            'priority': self._get_link_priority(link_type, url)
                        })

        return text_links

    def _extract_file_size(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج حجم الملف"""
        # محددات للبحث عن حجم الملف
        size_selectors = [
            '.file-size', '.download-size', '.size-info', '.size',
            '.file-info .size', '.download-info .size',
            '[class*="size"]', '[id*="size"]'
        ]

        # أنماط للبحث عن حجم الملف في النص
        size_patterns = [
            r'(\d+(?:\.\d+)?\s*(?:MB|KB|GB|mb|kb|gb))',
            r'Size:\s*(\d+(?:\.\d+)?\s*(?:MB|KB|GB|mb|kb|gb))',
            r'حجم:\s*(\d+(?:\.\d+)?\s*(?:MB|KB|GB|mb|kb|gb))',
            r'(\d+(?:\.\d+)?\s*(?:ميجا|كيلو|جيجا)(?:بايت)?)'
        ]

        # البحث باستخدام المحددات
        for selector in size_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                for pattern in size_patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        return match.group(1).strip()

        # البحث في النص العام
        page_text = soup.get_text()
        for pattern in size_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return None

    def _extract_additional_download_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات إضافية عن التحميل"""
        additional_info = {}

        # البحث عن تاريخ التحديث
        update_date = self._extract_update_date(soup)
        if update_date:
            additional_info['last_updated'] = update_date

        # البحث عن عدد التحميلات
        download_count = self._extract_download_count(soup)
        if download_count:
            additional_info['download_count'] = download_count

        # البحث عن متطلبات النظام
        requirements = self._extract_requirements(soup)
        if requirements:
            additional_info['requirements'] = requirements

        return additional_info

    def _extract_update_date(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج تاريخ آخر تحديث"""
        date_selectors = [
            '.last-updated', '.update-date', '.modified-date',
            '.post-date', '.entry-date', 'time[datetime]',
            '[class*="date"]', '[class*="updated"]'
        ]

        for selector in date_selectors:
            elements = soup.select(selector)
            for element in elements:
                # البحث في سمة datetime
                datetime_attr = element.get('datetime')
                if datetime_attr:
                    return datetime_attr

                # البحث في النص
                text = element.get_text(strip=True)
                if text and len(text) < 50:  # تواريخ عادة قصيرة
                    return text

        return None

    def _extract_download_count(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج عدد التحميلات"""
        count_patterns = [
            r'(\d+(?:,\d+)*)\s*(?:downloads?|تحميل|تحميلات)',
            r'Downloaded:\s*(\d+(?:,\d+)*)',
            r'تم التحميل:\s*(\d+(?:,\d+)*)'
        ]

        page_text = soup.get_text()
        for pattern in count_patterns:
            match = re.search(pattern, page_text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def _extract_requirements(self, soup: BeautifulSoup) -> Optional[str]:
        """استخراج متطلبات النظام"""
        req_selectors = [
            '.requirements', '.system-requirements', '.compatibility',
            '.minimum-requirements', '[class*="requirement"]'
        ]

        for selector in req_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                if text and len(text) < 200:  # متطلبات عادة قصيرة
                    return text

        return None

    def _is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url or len(url) < 10:
            return False

        url_lower = url.lower()

        # تجنب الصور الواضحة غير المرغوبة
        avoid_patterns = [
            'gravatar', 'avatar', 'profile', 'comment', 'user-image', 'users/', # إضافة 'users/'
            'author-photo', 'logo', 'icon', 'favicon', 'banner',
            'advertisement', 'ad-', '/ads/', 'sponsor'
        ]

        for pattern in avoid_patterns:
            if pattern in url_lower:
                return False

        # امتدادات الصور المقبولة
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
        has_image_extension = any(ext in url_lower for ext in image_extensions)

        # التحقق من المصادر الموثوقة
        # إعطاء أولوية لصور المود الأصلية
        trusted_sources = [
            'media.forgecdn.net/attachments/description/', # صور المود الأصلية
            'forgecdn.net',
            'r2.mcpedl.com',
            'mcpedl.com'
        ]
        is_trusted_source = any(source in url_lower for source in trusted_sources)

        # قبول الصور من المصادر الموثوقة أو التي لها امتدادات صور
        if is_trusted_source or has_image_extension:
            # تجنب الصور الصغيرة جداً (أيقونات)
            if any(size in url_lower for size in ['16x16', '32x32', '64x64', 'thumb']):
                return False
            return True

        return False


# دوال مساعدة للاستخدام السهل

def extract_mcpedl_mod_enhanced(url: str, gemini_api_key: str = None) -> Optional[Dict[str, Any]]:
    """استخراج بيانات المود من MCPEDL بشكل محسن"""
    extractor = EnhancedMCPEDLExtractor(gemini_api_key)
    return extractor.extract_mod_data(url)


def get_first_image_only(url: str, gemini_api_key: str = None) -> Optional[str]:
    """
    دالة مبسطة لاستخراج الصورة الأولى فقط من صفحة المود
    مع الاحتفاظ بجميع ميزات الفلترة الذكية
    
    Args:
        url: رابط صفحة المود
        gemini_api_key: مفتاح API لـ Gemini (اختياري)
        
    Returns:
        str: رابط الصورة الأولى فقط، أو None في حالة الفشل
    """
    print(f"🔍 استخراج الصورة الأولى فقط من: {url}")
    
    # استخدام المستخرج المحسن الكامل
    extractor = EnhancedMCPEDLExtractor(gemini_api_key)
    result = extractor.extract_mod_data(url)
    
    if result and 'image_urls' in result and result['image_urls']:
        first_image = result['image_urls'][0]
        print(f"✅ تم استخراج الصورة الأولى بنجاح: {first_image}")
        return first_image
    else:
        print("⚠️ لم يتم العثور على أي صورة")
        return None


def get_first_image_fast(url: str) -> Optional[str]:
    """
    دالة سريعة لاستخراج الصورة الأولى فقط من صفحة المود
    بدون استخدام الذكاء الاصطناعي أو المعالجة المتقدمة
    
    Args:
        url: رابط صفحة المود
        
    Returns:
        str: رابط الصورة الأولى فقط، أو None في حالة الفشل
    """
    print(f"🔍 استخراج سريع للصورة الأولى من: {url}")
    
    try:
        # جلب محتوى الصفحة
        if CLOUDSCRAPER_AVAILABLE:
            scraper = cloudscraper.create_scraper()
            response = scraper.get(url, timeout=30)
        else:
            response = requests.get(url, timeout=30, 
                                   headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'})
        
        response.raise_for_status()
        
        # تحليل HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # البحث عن الصورة الرئيسية بأولوية
        selectors = [
            '.featured-image img', 
            '.post-thumbnail img', 
            '.mod-main-image img',
            'img[src*="media.forgecdn.net/attachments/description"]',
            'img[src*="forgecdn"]',
            'img[src*="r2.mcpedl.com"]',
            '.entry-content img',
            '.post-content img',
            'img'  # آخر محاولة - أي صورة
        ]
        
        for selector in selectors:
            img = soup.select_one(selector)
            if img:
                src = (img.get('src') or 
                      img.get('data-src') or 
                      img.get('data-lazy-src') or 
                      img.get('data-original'))
                
                if src:
                    full_url = urljoin(url, src)
                    print(f"✅ تم استخراج الصورة الأولى بسرعة: {full_url}")
                    return full_url
        
        print("⚠️ لم يتم العثور على أي صورة")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في استخراج الصورة: {e}")
        return None


# اختبار سريع
if __name__ == "__main__":
    # مثال للاستخدام
    test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"

    print("\n🧪 اختبار المستخرج المحسن الكامل...")
    result = extract_mcpedl_mod_enhanced(test_url)

    if result:
        print(f"✅ نجح الاستخراج!")
        print(f"📝 اسم المود: {result.get('name')}")
        print(f"🖼️ عدد الصور: {len(result.get('image_urls', []))}")
        print(f"🌐 روابط التواصل: {len(result.get('creator_social_channels', []))}")
    else:
        print("❌ فشل الاستخراج")
        
    print("\n🧪 اختبار استخراج الصورة الأولى فقط...")
    first_image = get_first_image_only(test_url)
    if first_image:
        print(f"✅ الصورة الأولى: {first_image}")
    
    print("\n🧪 اختبار استخراج الصورة الأولى بسرعة...")
    fast_image = get_first_image_fast(test_url)
    if fast_image:
        print(f"✅ الصورة الأولى (سريع): {fast_image}")
