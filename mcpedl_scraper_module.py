# -*- coding: utf-8 -*-
"""
وحدة استخراج بيانات المودات من موقع mcpedl.com
تم تصميمها للتكامل مع أداة نشر المودات الموجودة
"""

import requests
import time
import re
import json
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Optional, Any

# محاولة استيراد cloudscraper لتجاوز حماية Cloudflare
try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
    print("cloudscraper متوفر - سيتم استخدامه لتجاوز الحماية")
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("cloudscraper غير متوفر - سيتم استخدام requests العادي")

# محاولة استيراد فلتر Gemini الذكي
try:
    from gemini_image_filter import GeminiImageFilter
    GEMINI_IMAGE_FILTER_AVAILABLE = True
    print("Gemini Image Filter متوفر للفلترة الذكية")
except ImportError:
    GEMINI_IMAGE_FILTER_AVAILABLE = False
    print("Gemini Image Filter غير متوفر - سيتم استخدام الفلترة التقليدية")

# محاولة استيراد مستخرج التواصل الاجتماعي الذكي
try:
    from gemini_social_extractor import GeminiSocialExtractor
    GEMINI_SOCIAL_EXTRACTOR_AVAILABLE = True
    print("Gemini Social Extractor متوفر لاستخراج التواصل الاجتماعي")
except ImportError:
    GEMINI_SOCIAL_EXTRACTOR_AVAILABLE = False
    print("Gemini Social Extractor غير متوفر - سيتم استخدام الاستخراج التقليدي")

class MCPEDLScraper:
    """كلاس استخراج بيانات المودات من mcpedl.com"""

    def __init__(self):
        # استخدام cloudscraper إذا كان متوفراً، وإلا استخدام requests العادي
        if CLOUDSCRAPER_AVAILABLE:
            self.session = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
            print("تم إنشاء جلسة cloudscraper")
        else:
            self.session = requests.Session()
            print("تم إنشاء جلسة requests عادية")

        # ترويسات محسنة لتجاوز الحماية
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })

        # إعدادات إضافية للجلسة
        self.session.max_redirects = 10

        # قائمة User Agents للتناوب
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        # محددات CSS محسنة لموقع mcpedl.com
        self.selectors = {
            'title': 'h1.entry-title, h1.post-title, .post-header h1, h1, .entry-header h1, .page-title',
            'description': '.entry-content p, .post-content p, .content p, article p, .post-body p, .entry-summary',
            'category': '.breadcrumbs a:last-of-type, .category-link, .post-categories a, .breadcrumb-item a, nav.breadcrumb a',
            'main_image': '.entry-content img:first-of-type, .post-content img:first-of-type, .featured-image img, .post-thumbnail img, .wp-post-image',
            'gallery_images': '.gallery img, .wp-block-gallery img, .entry-content img, .post-content img, .attachment-thumbnail, .size-thumbnail',
            'version': '.supported-versions, .minecraft-version, .version-info, .compatibility, .mcpe-version, .version-support',
            'download_link': '.download-link, .btn-download, a[href*="download"], .download-button, .dl-link, a[href*="mediafire"], a[href*="drive.google"]',
            'file_size': '.file-size, .download-size, .size-info, .filesize',
            'creator': '.author-info, .post-author, .creator-name, .by-author, .author-name, .post-meta .author',
            'social_links': '.social-links a, .author-social a, .contact-links a, .social-media a',
            'downloads_count': '.download-count, .downloads, .download-stats, .dl-count',
            'likes_count': '.likes-count, .rating, .votes, .post-rating'
        }

    def is_valid_mcpedl_url(self, url: str) -> bool:
        """التحقق من صحة رابط mcpedl.com"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower() in ['mcpedl.com', 'www.mcpedl.com']
        except:
            return False

    def fetch_page(self, url: str, max_retries: int = 5) -> Optional[BeautifulSoup]:
        """جلب وتحليل صفحة الويب مع تجاوز الحماية"""
        if not self.is_valid_mcpedl_url(url):
            return None

        for attempt in range(max_retries):
            try:
                # تناوب User Agent في كل محاولة
                if attempt > 0:
                    import random
                    user_agent = random.choice(self.user_agents)
                    self.session.headers['User-Agent'] = user_agent

                # إضافة Referer للمحاولات بعد الأولى
                if attempt > 0:
                    self.session.headers['Referer'] = 'https://mcpedl.com/'

                # تأخير متدرج بين المحاولات
                if attempt > 0:
                    delay = min(2 ** attempt, 10)  # حد أقصى 10 ثواني
                    time.sleep(delay)

                print(f"محاولة {attempt + 1}: جلب الصفحة...")

                # إضافة تأخير قبل الطلب لمحاكاة السلوك البشري
                if attempt == 0:
                    time.sleep(2)  # تأخير أولي

                # طلب الصفحة مع إعدابات محسنة
                response = self.session.get(
                    url,
                    timeout=45,  # زيادة المهلة الزمنية أكثر
                    allow_redirects=True,
                    verify=True,  # التحقق من SSL
                    stream=False  # تحميل المحتوى كاملاً
                )

                # تأخير إضافي بعد الطلب للسماح بتحميل JavaScript
                time.sleep(3)

                print(f"رمز الاستجابة: {response.status_code}")

                # التحقق من رمز الاستجابة
                if response.status_code == 403:
                    print("تم رفض الوصول (403) - محاولة تجاوز الحماية...")
                    continue
                elif response.status_code == 429:
                    print("تم تجاوز حد الطلبات (429) - انتظار أطول...")
                    time.sleep(30)
                    continue
                elif response.status_code == 503:
                    print("الخدمة غير متاحة (503) - انتظار...")
                    time.sleep(15)
                    continue

                response.raise_for_status()

                # التحقق من نوع المحتوى
                content_type = response.headers.get('content-type', '')
                if 'text/html' not in content_type:
                    print(f"نوع محتوى غير متوقع: {content_type}")
                    continue

                # التحقق من وجود محتوى
                if len(response.text) < 1000:
                    print(f"المحتوى قصير جداً ({len(response.text)} حرف) - قد يكون صفحة خطأ")
                    continue

                # التحقق من علامات الحماية
                if any(keyword in response.text.lower() for keyword in
                      ['cloudflare', 'ddos protection', 'access denied', 'blocked']):
                    print("تم اكتشاف حماية - محاولة أخرى...")
                    continue

                # تحليل HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # التحقق من وجود محتوى مفيد بشكل أكثر مرونة
                title_tag = soup.find('title')
                body_tag = soup.find('body')

                if not title_tag:
                    print("لا يوجد عنصر title - HTML غير مكتمل")
                    continue

                if not body_tag:
                    print("لا يوجد عنصر body - HTML غير مكتمل")
                    continue

                # التحقق من وجود محتوى نصي كافي في الصفحة
                page_text = soup.get_text()
                if len(page_text.strip()) < 500:
                    print(f"محتوى نصي قليل ({len(page_text.strip())} حرف) - قد يكون تحميل جزئي")
                    continue

                # التحقق من وجود كلمات مفتاحية تدل على صفحة mcpedl صحيحة
                title_text = title_tag.get_text().lower()
                if 'mcpedl' not in title_text and 'minecraft' not in title_text:
                    print(f"العنوان لا يحتوي على كلمات mcpedl المتوقعة: {title_text[:100]}")
                    # لا نتوقف هنا، قد تكون صفحة صحيحة

                # التحقق من وجود محتوى أساسي للمود
                main_content = soup.find(['article', 'main', '.entry-content', '.post-content'])
                if not main_content:
                    # محاولة البحث عن محتوى بطرق أخرى
                    content_indicators = soup.find_all(['h1', 'h2', 'p'], limit=10)
                    if len(content_indicators) < 3:
                        print("لا يوجد محتوى أساسي كافي - قد يكون تحميل جزئي")
                        continue

                # حفظ HTML للتشخيص (اختياري)
                if attempt == 0:  # حفظ فقط في المحاولة الأولى
                    try:
                        with open('debug_mcpedl_page.html', 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        print("تم حفظ HTML للتشخيص في debug_mcpedl_page.html")
                    except:
                        pass

                print("تم جلب الصفحة بنجاح!")
                return soup

            except requests.exceptions.Timeout:
                print(f"انتهت المهلة الزمنية في المحاولة {attempt + 1}")
            except requests.exceptions.ConnectionError:
                print(f"خطأ في الاتصال في المحاولة {attempt + 1}")
            except requests.exceptions.RequestException as e:
                print(f"خطأ في الطلب في المحاولة {attempt + 1}: {e}")
            except Exception as e:
                print(f"خطأ عام في المحاولة {attempt + 1}: {e}")

        print(f"فشل في جلب الصفحة بعد {max_retries} محاولات")
        return None

    def clean_text(self, text: str) -> str:
        """تنظيف النص من المسافات الزائدة والأحرف الخاصة"""
        if not text:
            return ""

        text = re.sub(r'\s+', ' ', text.strip())
        text = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF.,!?()-]', '', text)
        return text.strip()

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        selectors = self.selectors['title'].split(', ')

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = self.clean_text(element.get_text())
                if title:
                    return title

        # محاولة استخراج من title tag
        title_tag = soup.find('title')
        if title_tag:
            title = self.clean_text(title_tag.get_text())
            title = title.replace(' - MCPEDL', '').replace(' | MCPEDL', '')
            if title:
                return title

        return ""

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود بتنسيق سطر تحت سطر"""
        selectors = self.selectors['description'].split(', ')
        description_lines = []

        for selector in selectors:
            elements = soup.select(selector)
            for element in elements[:5]:  # أول 5 فقرات فقط
                text = self.clean_text(element.get_text())
                if text and len(text) > 20:  # تجاهل النصوص القصيرة جداً
                    # تقسيم النص إلى جمل وإضافة كل جملة في سطر منفصل
                    sentences = re.split(r'[.!?]+', text)
                    for sentence in sentences:
                        sentence = sentence.strip()
                        if sentence and len(sentence) > 10:
                            description_lines.append(sentence)

        # دمج الأسطر بدون فقرات (سطر تحت سطر)
        description = '\n'.join(description_lines[:15])  # حد أقصى 15 سطر
        if description:
            return description[:2000]  # حد أقصى للطول

        return ""

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # محاولة استخراج من breadcrumbs
        breadcrumbs = soup.select('.breadcrumbs a, .breadcrumb a')
        if breadcrumbs and len(breadcrumbs) > 1:
            category = self.clean_text(breadcrumbs[-2].get_text())
            if category and category.lower() not in ['home', 'mcpedl']:
                return category

        # محاولة استخراج من URL
        selectors = self.selectors['category'].split(', ')
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                category = self.clean_text(element.get_text())
                if category:
                    return category

        return "Addons"  # فئة افتراضية

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []

        # الصورة الرئيسية
        main_img_selectors = self.selectors['main_image'].split(', ')
        for selector in main_img_selectors:
            img = soup.select_one(selector)
            if img and img.get('src'):
                url = urljoin(base_url, img['src'])
                if self.validate_image_url(url):
                    image_urls.append(url)
                    break

        # صور المعرض
        gallery_selectors = self.selectors['gallery_images'].split(', ')
        for selector in gallery_selectors:
            images = soup.select(selector)
            for img in images[:10]:  # حد أقصى 10 صور
                src = img.get('src') or img.get('data-src')
                if src:
                    url = urljoin(base_url, src)
                    if self.validate_image_url(url) and url not in image_urls:
                        image_urls.append(url)

        # تطبيق فلتر Gemini الذكي إذا كان متوفراً
        if GEMINI_IMAGE_FILTER_AVAILABLE and image_urls:
            try:
                filtered_images = self._apply_gemini_filter(image_urls, base_url)
                if filtered_images:
                    print(f"🤖 Gemini AI فلتر الصور: {len(filtered_images)} من أصل {len(image_urls)}")
                    return filtered_images
                else:
                    print("⚠️ فلتر Gemini لم يختر أي صور، سيتم إرجاع الصور الأصلية")
            except Exception as e:
                print(f"⚠️ خطأ في فلتر Gemini: {e}")

        return image_urls

    def validate_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url:
            return False

        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
        url_lower = url.lower()

        return any(ext in url_lower for ext in image_extensions)

    def extract_versions(self, soup: BeautifulSoup) -> str:
        """استخراج إصدارات Minecraft المدعومة"""
        version_selectors = self.selectors['version'].split(', ')
        all_versions = []

        for selector in version_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text()
                versions = self.extract_version_numbers(text)
                all_versions.extend(versions)

        # البحث في النص العام للصفحة
        page_text = soup.get_text()
        page_versions = self.extract_version_numbers(page_text)
        all_versions.extend(page_versions)

        # إزالة التكرارات والترتيب
        unique_versions = sorted(list(set(all_versions)), reverse=True)

        if unique_versions:
            return ', '.join(unique_versions[:5])  # أول 5 إصدارات

        return "1.20+"  # إصدار افتراضي

    def extract_version_numbers(self, text: str) -> List[str]:
        """استخراج أرقام إصدارات Minecraft من النص"""
        if not text:
            return []

        version_pattern = r'1\.\d+(?:\.\d+)*'
        versions = re.findall(version_pattern, text)

        return sorted(list(set(versions)), reverse=True)

    def extract_download_info(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """استخراج معلومات التحميل (بدون رابط التحميل)"""
        download_info = {
            'download_url': '',  # لن يتم ملؤه
            'size': ''
        }

        # لا نستخرج رابط التحميل بناءً على طلب المستخدم
        # download_selectors = self.selectors['download_link'].split(', ')
        # for selector in download_selectors:
        #     link = soup.select_one(selector)
        #     if link and link.get('href'):
        #         download_url = urljoin(base_url, link['href'])
        #         download_info['download_url'] = download_url
        #         break

        # البحث عن حجم الملف
        size_selectors = self.selectors['file_size'].split(', ')
        for selector in size_selectors:
            element = soup.select_one(selector)
            if element:
                size = self.extract_file_size(element.get_text())
                if size:
                    download_info['size'] = size
                    break

        return download_info

    def extract_file_size(self, text: str) -> Optional[str]:
        """استخراج حجم الملف من النص"""
        if not text:
            return None

        size_pattern = r'(\d+(?:\.\d+)?)\s*(KB|MB|GB|kb|mb|gb)'
        match = re.search(size_pattern, text, re.IGNORECASE)

        if match:
            size, unit = match.groups()
            return f"{size} {unit.upper()}"

        return None

    def extract_creator_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """استخراج معلومات المطور بشكل مكثف"""
        creator_info = {
            'creator_name': '',
            'creator_contact_info': '',
            'creator_social_channels': []
        }

        # اسم المطور
        creator_selectors = self.selectors['creator'].split(', ')
        for selector in creator_selectors:
            element = soup.select_one(selector)
            if element:
                creator_name = self.clean_text(element.get_text())
                if creator_name:
                    creator_info['creator_name'] = creator_name
                    break

        # استخراج روابط التواصل الاجتماعي بشكل مكثف
        social_channels = self.extract_comprehensive_social_links(soup)
        creator_info['creator_social_channels'] = social_channels

        return creator_info

    def extract_comprehensive_social_links(self, soup: BeautifulSoup) -> List[str]:
        """استخراج روابط التواصل الاجتماعي بشكل شامل ومكثف"""
        social_channels = []
        found_urls = set()  # لتجنب التكرار

        # قائمة شاملة بمنصات التواصل الاجتماعي
        social_platforms = {
            'youtube.com': 'YouTube',
            'youtu.be': 'YouTube',
            'discord.gg': 'Discord',
            'discord.com': 'Discord',
            'twitter.com': 'Twitter',
            'x.com': 'Twitter',
            'instagram.com': 'Instagram',
            'facebook.com': 'Facebook',
            'tiktok.com': 'TikTok',
            'github.com': 'GitHub',
            'twitch.tv': 'Twitch',
            'reddit.com': 'Reddit',
            'telegram.me': 'Telegram',
            't.me': 'Telegram',
            'linkedin.com': 'LinkedIn',
            'snapchat.com': 'Snapchat'
        }

        # البحث في جميع الروابط في الصفحة
        all_links = soup.find_all('a', href=True)

        for link in all_links:
            href = link.get('href', '').strip()
            if not href or href in found_urls:
                continue

            href_lower = href.lower()

            # فحص كل منصة تواصل اجتماعي
            for platform_domain, platform_name in social_platforms.items():
                if platform_domain in href_lower:
                    # التحقق من أن الرابط ليس رابط عام للموقع
                    if self.is_valid_social_profile_url(href, platform_domain):
                        social_channels.append(f"{platform_name}: {href}")
                        found_urls.add(href)
                        break

        # البحث في النصوص للعثور على معرفات إضافية
        text_content = soup.get_text()
        social_mentions = self.extract_social_mentions_from_text(text_content)
        for mention in social_mentions:
            if mention not in [channel.split(': ')[1] for channel in social_channels]:
                platform = self.extract_social_platform(mention)
                social_channels.append(f"{platform}: {mention}")

        return social_channels[:10]  # حد أقصى 10 روابط

    def is_valid_social_profile_url(self, url: str, platform_domain: str) -> bool:
        """التحقق من أن الرابط يشير إلى ملف شخصي وليس الصفحة الرئيسية"""
        url_lower = url.lower()

        # استبعاد الروابط العامة
        invalid_patterns = [
            f'{platform_domain}/',
            f'{platform_domain}#',
            f'{platform_domain}?',
            'privacy',
            'terms',
            'about',
            'help',
            'support',
            'login',
            'signup'
        ]

        # التحقق من أن الرابط يحتوي على معرف مستخدم
        if any(pattern in url_lower for pattern in invalid_patterns):
            return False

        # التحقق من وجود معرف مستخدم
        path_parts = url.split('/')
        return len(path_parts) > 3 and len(path_parts[-1]) > 2

    def extract_social_mentions_from_text(self, text: str) -> List[str]:
        """استخراج إشارات التواصل الاجتماعي من النص"""
        mentions = []

        # البحث عن معرفات Discord
        discord_pattern = r'discord\.gg/[a-zA-Z0-9]+'
        discord_matches = re.findall(discord_pattern, text, re.IGNORECASE)
        for match in discord_matches:
            mentions.append(f"https://{match}")

        # البحث عن معرفات YouTube
        youtube_pattern = r'youtube\.com/[@\w]+'
        youtube_matches = re.findall(youtube_pattern, text, re.IGNORECASE)
        for match in youtube_matches:
            mentions.append(f"https://{match}")

        # البحث عن معرفات Twitter/X
        twitter_pattern = r'@\w{1,15}(?=\s|$|[^\w])'
        twitter_matches = re.findall(twitter_pattern, text)
        for match in twitter_matches:
            if len(match) > 2:  # تجاهل المعرفات القصيرة جداً
                mentions.append(f"https://twitter.com/{match[1:]}")

        return mentions

    def extract_social_platform(self, url: str) -> str:
        """تحديد منصة التواصل الاجتماعي من الرابط"""
        if not url:
            return "unknown"

        url_lower = url.lower()

        if 'youtube.com' in url_lower or 'youtu.be' in url_lower:
            return 'YouTube'
        elif 'discord' in url_lower:
            return 'Discord'
        elif 'twitter.com' in url_lower or 'x.com' in url_lower:
            return 'Twitter/X'
        elif 'instagram.com' in url_lower:
            return 'Instagram'
        elif 'facebook.com' in url_lower:
            return 'Facebook'
        elif 'tiktok.com' in url_lower:
            return 'TikTok'
        elif 'github.com' in url_lower:
            return 'GitHub'
        else:
            return 'Other'

    def scrape_mod_data(self, url: str) -> Optional[Dict[str, Any]]:
        """استخراج جميع بيانات المود من الرابط"""
        # جلب الصفحة
        soup = self.fetch_page(url)
        if not soup:
            return None

        try:
            # استخراج البيانات الأساسية
            mod_data = {
                'name': self.extract_title(soup),
                'description': self.extract_description(soup),
                'category': self.extract_category(soup),
                'image_urls': self.extract_images(soup, url),
                'version': self.extract_versions(soup),
                'source_url': url
            }

            # معلومات التحميل
            download_info = self.extract_download_info(soup, url)
            mod_data.update(download_info)

            # معلومات المطور
            creator_info = self.extract_creator_info(soup)
            mod_data.update(creator_info)

            # التحقق من البيانات الأساسية
            if not mod_data['name']:
                return None

            return mod_data

        except Exception:
            return None

    def _apply_gemini_filter(self, image_urls: List[str], base_url: str) -> List[str]:
        """تطبيق فلتر Gemini الذكي لاختيار الصور المتعلقة بالمود فقط"""
        try:
            # الحصول على مفتاح API
            api_key = self._get_gemini_api_key()
            if not api_key:
                print("⚠️ مفتاح Gemini API غير متوفر للفلترة الذكية")
                return image_urls

            # إنشاء فلتر Gemini
            gemini_filter = GeminiImageFilter(api_key)
            if not gemini_filter.model:
                print("⚠️ لم يتم تهيئة Gemini للفلترة")
                return image_urls

            # استخراج معلومات المود من base_url
            mod_name = self._extract_mod_name_from_url(base_url)
            mod_description = "Minecraft mod or addon"  # وصف عام

            print(f"🤖 بدء فلترة الصور باستخدام Gemini AI للمود: {mod_name}")

            # تطبيق الفلتر
            filtered_images = gemini_filter.filter_mod_images(image_urls, mod_name, mod_description)

            return filtered_images

        except Exception as e:
            print(f"❌ خطأ في تطبيق فلتر Gemini: {e}")
            return image_urls

    def _extract_mod_name_from_url(self, url: str) -> str:
        """استخراج اسم المود من الرابط"""
        try:
            parsed = urlparse(url)
            path_parts = parsed.path.strip('/').split('/')

            # آخر جزء في المسار عادة ما يكون اسم المود
            if path_parts:
                mod_name = path_parts[-1].replace('-', ' ').replace('_', ' ')
                # إزالة الأرقام والإصدارات
                mod_name = re.sub(r'v?\d+[\.\d]*', '', mod_name).strip()
                return mod_name.title()

            return "Unknown Mod"

        except Exception:
            return "Unknown Mod"

    def _get_gemini_api_key(self) -> Optional[str]:
        """الحصول على مفتاح Gemini API"""
        try:
            import os

            # البحث في متغيرات البيئة
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                return api_key

            # البحث في ملف config
            config_files = ['config.json', 'settings.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    try:
                        import json
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)

                        # البحث في gemini_api_keys (قائمة)
                        if 'gemini_api_keys' in config and config['gemini_api_keys']:
                            keys = config['gemini_api_keys']
                            if isinstance(keys, list) and len(keys) > 0:
                                return keys[0]  # أول مفتاح في القائمة

                        # البحث في gemini_api_key
                        if 'gemini_api_key' in config and config['gemini_api_key']:
                            return config['gemini_api_key']

                    except Exception as e:
                        print(f"خطأ في قراءة {config_file}: {e}")
                        continue

            return None

        except Exception as e:
            print(f"خطأ في الحصول على مفتاح API: {e}")
            return None

    def _extract_smart_social_links(self, html_content: str, url: str, mod_data: Dict) -> Dict[str, List[str]]:
        """استخراج روابط التواصل الاجتماعي الذكي باستخدام Gemini"""
        if not GEMINI_SOCIAL_EXTRACTOR_AVAILABLE:
            return {}

        try:
            # الحصول على مفتاح API
            api_key = self._get_gemini_api_key()
            if not api_key:
                print("⚠️ مفتاح Gemini API غير متوفر لاستخراج التواصل الاجتماعي")
                return {}

            # إنشاء مستخرج التواصل الاجتماعي
            social_extractor = GeminiSocialExtractor(api_key)
            if not social_extractor.model:
                print("⚠️ لم يتم تهيئة Gemini لاستخراج التواصل الاجتماعي")
                return {}

            # استخراج روابط التواصل
            social_links = social_extractor.extract_creator_social_links(
                html_content,
                mod_data.get('name', ''),
                mod_data.get('creator_name', ''),
                url
            )

            return social_links

        except Exception as e:
            print(f"❌ خطأ في استخراج التواصل الاجتماعي الذكي: {e}")
            return {}

    def _format_social_links(self, social_links: Dict[str, List[str]]) -> List[str]:
        """تنسيق روابط التواصل الاجتماعي للعرض"""
        formatted_links = []

        for platform, urls in social_links.items():
            for url in urls:
                formatted_links.append(f"{platform}: {url}")

        return formatted_links

    def close(self):
        """إغلاق الجلسة"""
        self.session.close()

# دالة مساعدة للتكامل مع الأداة الرئيسية
def scrape_mcpedl_mod(url: str) -> Optional[Dict[str, Any]]:
    """دالة رئيسية لاستخراج بيانات مود من mcpedl.com مع طرق متعددة"""

    # المحاولة الأولى: الطريقة المحسنة مع cloudscraper
    print("🔄 محاولة الاستخراج بالطريقة المحسنة...")

    try:
        # استخدام cloudscraper لجلب HTML
        if CLOUDSCRAPER_AVAILABLE:
            scraper = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
        else:
            import requests
            scraper = requests.Session()
            scraper.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })

        response = scraper.get(url, timeout=30)

        if response.status_code == 200 and len(response.text) > 5000:
            print("✅ تم جلب HTML بنجاح")

            # استخدام المستخرج المحسن مع إنشاء أوصاف ذكية
            from mcpedl_extractor_fixed import MCPEDLExtractorFixed
            extractor = MCPEDLExtractorFixed()
            mod_data = extractor.extract_mod_data(response.text, url, generate_ai_descriptions=True)

            if mod_data and mod_data.get('name'):
                print("✅ نجح الاستخراج بالطريقة المحسنة")

                # طباعة ملخص البيانات المستخرجة
                print(f"📋 ملخص البيانات:")
                print(f"   - الاسم: {mod_data['name']}")
                print(f"   - الفئة: {mod_data['category']}")
                print(f"   - المطور: {mod_data['creator_name']}")
                print(f"   - الإصدار: {mod_data['version']}")
                print(f"   - الحجم: {mod_data['size']}")
                print(f"   - عدد الصور: {len(mod_data['image_urls'])}")
                print(f"   - طول الوصف الإنجليزي: {len(mod_data['description'])} حرف")
                print(f"   - طول الوصف العربي: {len(mod_data['description_arabic'])} حرف")

                return mod_data
            else:
                print("⚠️ فشل الاستخراج - البيانات غير مكتملة")
        else:
            print(f"⚠️ مشكلة في جلب HTML: {response.status_code}")

    except Exception as e:
        print(f"❌ خطأ في الطريقة المحسنة: {e}")

    # المحاولة الثانية: الطريقة الأصلية كـ fallback
    print("🔄 محاولة الاستخراج بالطريقة الأصلية...")
    scraper = MCPEDLScraper()

    try:
        mod_data = scraper.scrape_mod_data(url)

        if mod_data and mod_data.get('name'):
            print("✅ نجح الاستخراج بالطريقة الأصلية")
            return mod_data
        else:
            print("⚠️ فشل الاستخراج بالطريقة الأصلية - البيانات غير مكتملة")

    except Exception as e:
        print(f"❌ خطأ في الطريقة الأصلية: {e}")
    finally:
        scraper.close()

    # المحاولة الثانية: استخدام Selenium كبديل
    print("🔄 محاولة الاستخراج باستخدام Selenium...")

    try:
        # محاولة استيراد واستخدام Selenium
        from mcpedl_selenium_scraper import scrape_mcpedl_with_selenium, SELENIUM_AVAILABLE

        if SELENIUM_AVAILABLE:
            mod_data = scrape_mcpedl_with_selenium(url)

            if mod_data and mod_data.get('name'):
                print("✅ نجح الاستخراج باستخدام Selenium")
                return mod_data
            else:
                print("⚠️ فشل الاستخراج باستخدام Selenium")
        else:
            print("⚠️ Selenium غير متوفر - تثبيت: pip install selenium")

    except ImportError:
        print("⚠️ لا يمكن استيراد وحدة Selenium")
    except Exception as e:
        print(f"❌ خطأ في Selenium: {e}")

    print("❌ فشل في جميع طرق الاستخراج")
    print("💡 نصائح:")
    print("  1. تأكد من الاتصال بالإنترنت")
    print("  2. جرب رابط مود آخر")
    print("  3. تثبيت selenium: pip install selenium")
    print("  4. قد يكون الموقع يحجب الطلبات مؤقتاً")

    return None

# تصدير المتغير للاستخدام في mod_processor.py
MCPEDL_SCRAPER_AVAILABLE = True
