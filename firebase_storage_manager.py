# -*- coding: utf-8 -*-
"""
مدير Firebase Storage لتخزين ملفات المودات
Firebase Storage Manager for Mod Files
"""

import os
import json
import time
import hashlib
import requests
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
import tempfile

try:
    import firebase_admin
    from firebase_admin import credentials, storage
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    print("⚠️ firebase-admin غير متوفر. سيتم تعطيل رفع الملفات.")

class FirebaseStorageManager:
    """مدير Firebase Storage لتخزين ملفات المودات"""
    
    def __init__(self, config_path: str = "firebase_config.json"):
        """تهيئة مدير Firebase Storage"""
        self.app = None
        self.bucket = None
        self.config = None
        self.is_initialized = False
        
        # تحميل إعدادات Firebase
        self.config = self._load_firebase_config(config_path)
        
        if FIREBASE_AVAILABLE and self.config:
            self._initialize_firebase()
        else:
            print("❌ Firebase غير متوفر أو لم يتم تكوينه بشكل صحيح")

    def _load_firebase_config(self, config_path: str) -> Optional[Dict[str, Any]]:
        """تحميل إعدادات Firebase"""
        try:
            # محاولة تحميل من ملف الإعدادات
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ تم تحميل إعدادات Firebase من {config_path}")
                return config
            
            # إنشاء إعدادات افتراضية
            default_config = {
                "type": "service_account",
                "project_id": "download-e33a2",
                "storage_bucket": "download-e33a2.firebasestorage.app",
                "apiKey": "AIzaSyB3t2Ae-24DWUQJxwZ5LCFZVZov0ncaC8c",
                "authDomain": "download-e33a2.firebaseapp.com",
                "messagingSenderId": "************",
                "appId": "1:************:web:df601f640fb82d9c42bc46"
            }
            
            # حفظ الإعدادات الافتراضية
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            
            print(f"📝 تم إنشاء ملف إعدادات Firebase الافتراضي: {config_path}")
            print("⚠️ يرجى إضافة مفتاح الخدمة (service account key) للملف")
            
            return default_config
            
        except Exception as e:
            print(f"❌ خطأ في تحميل إعدادات Firebase: {e}")
            return None

    def _initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            # التحقق من وجود التطبيق مسبقاً
            try:
                self.app = firebase_admin.get_app()
                print("✅ تم العثور على تطبيق Firebase موجود")
            except ValueError:
                # إنشاء تطبيق جديد
                if self.config.get("private_key") and "YOUR_PRIVATE_KEY" not in str(self.config.get("private_key")):
                    # استخدام مفتاح الخدمة إذا كان صحيحاً
                    try:
                        cred = credentials.Certificate(self.config)
                        self.app = firebase_admin.initialize_app(cred, {
                            'storageBucket': self.config['storage_bucket']
                        })
                        print("✅ تم تهيئة Firebase باستخدام مفتاح الخدمة")
                    except Exception as cert_error:
                        print(f"⚠️ فشل استخدام مفتاح الخدمة: {cert_error}")
                        # محاولة استخدام الطريقة البديلة
                        self._try_alternative_initialization()
                        return
                else:
                    # استخدام الطريقة البديلة
                    self._try_alternative_initialization()
                    return

            # الحصول على bucket
            self.bucket = storage.bucket(app=self.app)
            self.is_initialized = True

            print(f"🔥 Firebase Storage جاهز: {self.config['storage_bucket']}")

        except Exception as e:
            print(f"❌ خطأ في تهيئة Firebase: {e}")
            print("🔄 محاولة استخدام الطريقة البديلة...")
            self._try_alternative_initialization()

    def _try_alternative_initialization(self):
        """محاولة تهيئة Firebase بطريقة بديلة"""
        try:
            # محاولة استخدام متغيرات البيئة أو الإعدادات الافتراضية
            import os

            # تعيين متغيرات البيئة إذا لم تكن موجودة
            if not os.environ.get('GOOGLE_APPLICATION_CREDENTIALS'):
                # إنشاء ملف مؤقت للإعدادات
                temp_config = {
                    "type": "service_account",
                    "project_id": self.config.get('project_id'),
                    "private_key_id": "dummy",
                    "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB\n-----END PRIVATE KEY-----\n",
                    "client_email": f"firebase-adminsdk@{self.config.get('project_id')}.iam.gserviceaccount.com",
                    "client_id": "dummy",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token"
                }

                # حفظ في ملف مؤقت
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(temp_config, f)
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = f.name

            # محاولة التهيئة بدون مفتاح خدمة
            self.app = firebase_admin.initialize_app(options={
                'storageBucket': self.config['storage_bucket']
            })

            self.bucket = storage.bucket(app=self.app)
            self.is_initialized = True

            print("✅ تم تهيئة Firebase باستخدام الطريقة البديلة")
            print(f"🔥 Firebase Storage جاهز: {self.config['storage_bucket']}")

        except Exception as e:
            print(f"❌ فشل في جميع طرق التهيئة: {e}")
            print("💡 نصائح لحل المشكلة:")
            print("   1. احصل على مفتاح خدمة صحيح من Firebase Console")
            print("   2. تأكد من تفعيل Firebase Storage في مشروعك")
            print("   3. راجع دليل FIREBASE_STORAGE_GUIDE.md للتفاصيل")
            self.is_initialized = False

    def check_connection(self) -> Dict[str, Any]:
        """فحص الاتصال مع Firebase Storage"""
        status = {
            'connected': False,
            'bucket_accessible': False,
            'can_upload': False,
            'can_download': False,
            'error': None,
            'bucket_name': None,
            'test_file_url': None
        }
        
        try:
            if not self.is_initialized:
                status['error'] = "Firebase غير مهيأ"
                return status
            
            status['connected'] = True
            status['bucket_name'] = self.config.get('storage_bucket')
            
            # اختبار الوصول للـ bucket
            try:
                # محاولة قائمة الملفات (أول 1 ملف فقط)
                blobs = list(self.bucket.list_blobs(max_results=1))
                status['bucket_accessible'] = True
                
                # اختبار رفع ملف تجريبي
                test_content = f"Firebase test - {int(time.time())}"
                test_blob_name = f"test/connection_test_{int(time.time())}.txt"
                
                blob = self.bucket.blob(test_blob_name)
                blob.upload_from_string(test_content, content_type='text/plain')
                
                status['can_upload'] = True
                
                # اختبار تحميل الملف
                downloaded_content = blob.download_as_text()
                if downloaded_content == test_content:
                    status['can_download'] = True
                
                # الحصول على رابط التحميل
                blob.make_public()
                status['test_file_url'] = blob.public_url
                
                # حذف الملف التجريبي
                blob.delete()
                
            except Exception as e:
                status['error'] = f"خطأ في اختبار الـ bucket: {e}"
                
        except Exception as e:
            status['error'] = f"خطأ في فحص الاتصال: {e}"
        
        return status

    def upload_mod_file(self, file_url: str, mod_name: str, file_type: str = "addon") -> Optional[Dict[str, Any]]:
        """رفع ملف المود إلى Firebase Storage"""
        if not self.is_initialized:
            print("❌ Firebase غير مهيأ")
            return None
        
        try:
            print(f"📤 بدء رفع ملف المود: {mod_name}")
            
            # تحميل الملف من الرابط
            print(f"⬇️ تحميل الملف من: {file_url}")
            response = requests.get(file_url, timeout=60, stream=True)
            response.raise_for_status()
            
            # التحقق من نوع الملف
            content_type = response.headers.get('content-type', 'application/octet-stream')
            file_size = int(response.headers.get('content-length', 0))
            
            print(f"📊 حجم الملف: {file_size / (1024*1024):.2f} MB")
            
            # إنشاء اسم ملف فريد
            file_extension = self._get_file_extension(file_url, content_type)
            timestamp = int(time.time())
            safe_mod_name = self._sanitize_filename(mod_name)
            blob_name = f"mods/{file_type}/{safe_mod_name}_{timestamp}{file_extension}"
            
            # رفع الملف
            blob = self.bucket.blob(blob_name)
            
            # إضافة metadata
            blob.metadata = {
                'mod_name': mod_name,
                'file_type': file_type,
                'original_url': file_url,
                'upload_time': str(timestamp),
                'file_size': str(file_size)
            }
            
            # رفع البيانات
            print("⬆️ رفع الملف إلى Firebase Storage...")
            blob.upload_from_string(response.content, content_type=content_type)
            
            # جعل الملف عام للتحميل
            blob.make_public()
            
            # إنشاء معلومات الملف المرفوع
            upload_info = {
                'firebase_url': blob.public_url,
                'blob_name': blob_name,
                'file_size': file_size,
                'content_type': content_type,
                'upload_time': timestamp,
                'mod_name': mod_name,
                'file_type': file_type,
                'original_url': file_url
            }
            
            print(f"✅ تم رفع الملف بنجاح!")
            print(f"🔗 رابط التحميل: {blob.public_url}")
            
            return upload_info
            
        except Exception as e:
            print(f"❌ خطأ في رفع الملف: {e}")
            return None

    def _get_file_extension(self, url: str, content_type: str) -> str:
        """استخراج امتداد الملف"""
        # محاولة استخراج من الرابط
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        if '.' in path:
            extension = os.path.splitext(path)[1]
            if extension:
                return extension
        
        # استخراج من نوع المحتوى
        content_type_map = {
            'application/zip': '.zip',
            'application/x-zip-compressed': '.zip',
            'application/octet-stream': '.mcpack',
            'application/vnd.android.package-archive': '.apk'
        }
        
        return content_type_map.get(content_type, '.mcpack')

    def _sanitize_filename(self, filename: str) -> str:
        """تنظيف اسم الملف"""
        # إزالة الأحرف غير المسموحة
        import re
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '-', sanitized)
        return sanitized.strip('-')[:50]  # حد أقصى 50 حرف

    def list_uploaded_mods(self, limit: int = 50) -> List[Dict[str, Any]]:
        """قائمة المودات المرفوعة"""
        if not self.is_initialized:
            return []
        
        try:
            mods = []
            blobs = self.bucket.list_blobs(prefix='mods/', max_results=limit)
            
            for blob in blobs:
                mod_info = {
                    'name': blob.name,
                    'public_url': blob.public_url if blob.public_url_set else None,
                    'size': blob.size,
                    'created': blob.time_created,
                    'updated': blob.updated,
                    'metadata': blob.metadata or {}
                }
                mods.append(mod_info)
            
            return mods
            
        except Exception as e:
            print(f"❌ خطأ في جلب قائمة المودات: {e}")
            return []

    def delete_mod_file(self, blob_name: str) -> bool:
        """حذف ملف مود"""
        if not self.is_initialized:
            return False
        
        try:
            blob = self.bucket.blob(blob_name)
            blob.delete()
            print(f"✅ تم حذف الملف: {blob_name}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حذف الملف: {e}")
            return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """إحصائيات التخزين"""
        if not self.is_initialized:
            return {}
        
        try:
            stats = {
                'total_files': 0,
                'total_size': 0,
                'mod_files': 0,
                'mod_size': 0,
                'bucket_name': self.config.get('storage_bucket')
            }
            
            # حساب الإحصائيات
            blobs = self.bucket.list_blobs()
            for blob in blobs:
                stats['total_files'] += 1
                stats['total_size'] += blob.size or 0
                
                if blob.name.startswith('mods/'):
                    stats['mod_files'] += 1
                    stats['mod_size'] += blob.size or 0
            
            return stats
            
        except Exception as e:
            print(f"❌ خطأ في جلب الإحصائيات: {e}")
            return {}


# دالة مساعدة للاستخدام السهل
def create_firebase_manager() -> Optional[FirebaseStorageManager]:
    """إنشاء مدير Firebase Storage مع حلول بديلة"""

    # تغيير ترتيب الأولويات: البدء بـ Firebase Web API
    # أولاً: محاولة Firebase Web API (الأولوية الأولى الآن)
    try:
        print("🔄 محاولة استخدام Firebase Web API...")
        from firebase_web_storage import FirebaseWebStorage
        web_storage = FirebaseWebStorage()
        if web_storage.is_initialized:
            # اختبار سريع للاتصال
            status = web_storage.check_connection()
            if status.get('connected'):
                print("✅ تم تهيئة Firebase Web Storage بنجاح")
                return web_storage
            else:
                print(f"⚠️ Firebase Web API غير متاح: {status.get('error', 'خطأ غير معروف')}")
    except Exception as web_error:
        print(f"⚠️ فشل Firebase Web API: {web_error}")

    # ثانياً: محاولة Firebase Admin SDK
    try:
        print("🔄 محاولة استخدام Firebase Admin SDK...")
        manager = FirebaseStorageManager()
        if manager.is_initialized:
            print("✅ تم تهيئة Firebase Admin SDK")
            return manager
    except Exception as admin_error:
        print(f"⚠️ فشل Firebase Admin SDK: {admin_error}")
    
    # ثالثاً: محاولة نظام التخزين المحلي (الأولوية الأخيرة الآن)
    try:
        print("🔄 محاولة استخدام نظام التخزين المحلي...")
        from local_file_storage import LocalFileStorage
        local_storage = LocalFileStorage()
        if local_storage.is_initialized:
            print("✅ تم تهيئة نظام التخزين المحلي بنجاح")
            return local_storage
    except Exception as local_error:
        print(f"⚠️ فشل نظام التخزين المحلي: {local_error}")

    # إذا فشل كل شيء
    print("❌ فشل في جميع أنظمة التخزين")
    print("💡 نصائح:")
    print("   - تأكد من صلاحيات الكتابة في مجلد المشروع")
    print("   - راجع إعدادات Firebase في FIREBASE_TROUBLESHOOTING.md")
    print("   - تحقق من اتصالك بالإنترنت")

    return None


# اختبار سريع
if __name__ == "__main__":
    print("🧪 اختبار Firebase Storage Manager...")
    
    manager = create_firebase_manager()
    
    if manager and manager.is_initialized:
        print("✅ تم تهيئة Firebase بنجاح")
        
        # فحص الاتصال
        status = manager.check_connection()
        print(f"📊 حالة الاتصال: {status}")
        
        # إحصائيات التخزين
        stats = manager.get_storage_stats()
        print(f"📈 إحصائيات التخزين: {stats}")
        
    else:
        print("❌ فشل في تهيئة Firebase")
